<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    android:sharedUserId="android.uid.system"
    xmlns:tools="http://schemas.android.com/tools">

    <!-- 现有权限 -->
    <uses-permission android:name="android.permission.CAMERA" />
    <!-- 添加开机自启动权限 -->
    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />

    <uses-permission android:name="android.permission.RECORD_AUDIO" />
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE"
        android:maxSdkVersion="32" />
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE"/>
    <!-- 确保有以下权限 -->
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    
<!--    &lt;!&ndash; 屏幕录制相关权限 &ndash;&gt;-->
<!--    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />-->
<!--    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />-->
<!--    <uses-permission android:name="android.permission.CAPTURE_VIDEO_OUTPUT" -->
<!--        tools:ignore="ProtectedPermissions" />-->

    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE"/>
    <uses-permission android:name="android.permission.CHANGE_WIFI_STATE"/>
    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION"/>
    <uses-permission android:name="android.permission.TETHER_PRIVILEGED"/>
    <uses-permission android:name="android.permission.WRITE_SETTINGS" />
    <uses-permission android:name="android.permission.MANAGE_WIFI_HOTSPOT"/>
    <uses-permission android:name="android.permission.CONNECTIVITY_INTERNAL" tools:ignore="ProtectedPermissions"/>
    <uses-permission android:name="android.permission.START_TETHERING" tools:ignore="ProtectedPermissions"/>
    
    <!-- TV模式所需权限 -->
    <uses-permission android:name="com.android.providers.tv.permission.READ_EPG_DATA" />

    <!-- 声明TV功能 -->
    <uses-feature android:name="android.software.live_tv" android:required="false" />

    <!-- 网络相关权限 -->
    <uses-permission android:name="android.permission.NEARBY_WIFI_DEVICES" />

    <!-- 要求后置摄像头 -->
    <uses-feature android:name="android.hardware.camera" />
    <application
        android:allowBackup="true"
        android:dataExtractionRules="@xml/data_extraction_rules"
        android:fullBackupContent="@xml/backup_rules"
        tools:replace="android:allowBackup"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:roundIcon="@mipmap/ic_launcher_round"
        android:supportsRtl="true"
        android:theme="@style/Theme.MediacodecNew"
        tools:targetApi="31">
        <!-- MainActivity 作为主启动Activity -->
        <activity
            android:name="com.android.rockchip.camera2.integrated.MainActivity"
            android:exported="true">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>
        <!-- 添加 VideoDecoderActivity 的声明 -->
        <activity android:name="com.android.rockchip.camera2.separated.VideoDecoderActivity"
            tools:ignore="DuplicateActivity" />
        <!-- 添加 MediaBrowserActivity 的声明 -->
        <activity android:name="com.android.rockchip.camera2.separated.MediaBrowserActivity" />
        <!-- 添加 ImageViewerActivity 的声明 -->
        <activity android:name="com.android.rockchip.camera2.separated.ImageViewerActivity" />

        <!-- integrated版本的Activity声明 -->
        <activity android:name="com.android.rockchip.camera2.integrated.browser.MediaBrowserActivity" />
        <activity android:name="com.android.rockchip.camera2.integrated.browser.ImageViewerActivity" />
        <activity android:name="com.android.rockchip.camera2.integrated.browser.TpVideoPlayerActivity" />
        <activity android:name="com.android.rockchip.camera2.integrated.browser.ImageVideoCompareActivity" />
        <activity android:name="com.android.rockchip.camera2.separated.TpVideoPlayerActivity" />

        <service
            android:name="com.touptek.video.internal.rtsp.service.RTSPService"
            android:enabled="true"
            android:exported="false"
            android:foregroundServiceType="mediaProjection"
            android:stopWithTask="false" />
    </application>


</manifest>