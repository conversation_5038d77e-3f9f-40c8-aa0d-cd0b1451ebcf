package com.touptek.video;

import android.media.MediaCodecInfo;
import android.util.Size;

/**
 * TpVideoConfig - ToupTek视频配置类
 * <p>
 * 提供完整的视频录制配置选项，支持Builder模式创建和预设配置模板。
 * 独立于现有的EncoderConfig，提供更灵活和专业的配置能力。
 * </p>
 */
public class TpVideoConfig {
    private static final String TAG = "TpVideoConfig";
    
    // ===== 必需参数 =====
    private final int width;              // 视频宽度
    private final int height;             // 视频高度
    private final int frameRate;          // 帧率 (fps)
    private final int bitRate;            // 比特率 (bps)
    
    // ===== 可选参数（有默认值） =====
    private final VideoCodec codec;       // 编码格式，默认H.264
    private final int keyFrameInterval;   // 关键帧间隔，默认1秒
    private final BitrateMode bitrateMode; // 比特率模式，默认CBR
    
    // 私有构造函数，强制使用Builder
    private TpVideoConfig(Builder builder) {
        this.width = builder.width;
        this.height = builder.height;
        this.frameRate = builder.frameRate;
        this.bitRate = builder.bitRate;
        this.codec = builder.codec;
        this.keyFrameInterval = builder.keyFrameInterval;
        this.bitrateMode = builder.bitrateMode;
    }

    /**
     * 创建默认1080P配置
     * @return 1080P 60fps 8Mbps配置
     */
    public static TpVideoConfig createDefault1080P() {
        return new Builder(1920, 1080)
            .setBitRate(8_000_000)
            .setKeyFrameInterval(1)
            .build();
    }
    
    /**
     * 创建默认4K配置
     * @return 4K 30fps 20Mbps配置
     */
    public static TpVideoConfig createDefault4K() {
        return new Builder(3840, 2160)
            .setBitRate(20_000_000)
            .setKeyFrameInterval(1)
            .build();
    }

    
    // ===== Builder模式 =====
    public static class Builder {
        // 必需参数
        private final int width;
        private final int height;
        private final int frameRate;
        
        // 可选参数（设置默认值）
        private int bitRate;
        private VideoCodec codec = VideoCodec.H264;
        private int keyFrameInterval = 1;
        private BitrateMode bitrateMode = BitrateMode.CBR;
        
        /**
         * 构造Builder
         * @param width 视频宽度
         * @param height 视频高度
         */
        public Builder(int width, int height) {
            this.width = width;
            this.height = height;
            this.frameRate = 60;
            this.bitRate = calculateDefaultBitRate(width, height, frameRate);
        }
        
        public Builder setBitRate(int bitRate) {
            this.bitRate = bitRate;
            return this;
        }
        
        public Builder setCodec(VideoCodec codec) {
            this.codec = codec;
            return this;
        }
        
        public Builder setKeyFrameInterval(int seconds) {
            this.keyFrameInterval = seconds;
            return this;
        }
        
        public Builder setBitrateMode(BitrateMode mode) {
            this.bitrateMode = mode;
            return this;
        }

        /**
         * 构建TpVideoConfig实例
         * @return 配置好的TpVideoConfig
         * @throws IllegalArgumentException 如果参数不合法
         */
        public TpVideoConfig build() {
            // 参数验证
            validateParameters();
            
            return new TpVideoConfig(this);
        }
        
        /**
         * 验证参数合法性
         */
        private void validateParameters() {
            if (width <= 0 || height <= 0) {
                throw new IllegalArgumentException("分辨率必须大于0");
            }
            if (bitRate <= 0) {
                throw new IllegalArgumentException("比特率必须大于0");
            }
            if (keyFrameInterval <= 0) {
                throw new IllegalArgumentException("关键帧间隔必须大于0");
            }
        }
        
        /**
         * 计算默认比特率
         */
        private int calculateDefaultBitRate(int width, int height, int fps) {
            long pixels = (long) width * height;
            
            if (pixels >= 3840L * 2160L) {
                // 4K: 15-25 Mbps
                return fps >= 60 ? 25_000_000 : 20_000_000;
            } else if (pixels >= 1920L * 1080L) {
                // 1080P: 6-10 Mbps
                return fps >= 60 ? 10_000_000 : 8_000_000;
            } else if (pixels >= 1280L * 720L) {
                // 720P: 3-5 Mbps
                return fps >= 60 ? 5_000_000 : 4_000_000;
            } else {
                // 其他: 1-3 Mbps
                return Math.max(1_000_000, (int) (pixels * fps * 0.1));
            }
        }
    }

    
    // ===== Getter方法 =====
    public int getWidth() { return width; }
    public int getHeight() { return height; }
    public int getFrameRate() { return frameRate; }
    public int getBitRate() { return bitRate; }
    public VideoCodec getCodec() { return codec; }
    public int getKeyFrameInterval() { return keyFrameInterval; }
    public BitrateMode getBitrateMode() { return bitrateMode; }

    public Size getSize() {
        return new Size(width, height);
    }
    

    
    @Override
    public String toString() {
        return String.format("TpVideoConfig{%dx%d@%dfps, %s, %dkbps}", 
            width, height, frameRate, codec.name(), bitRate / 1000);
    }
    


    // ===== 枚举定义 =====

    /**
     * 视频编码格式
     */
    public enum VideoCodec {
        H264("video/avc"),
        H265("video/hevc");

        private final String mimeType;

        VideoCodec(String mimeType) {
            this.mimeType = mimeType;
        }

        public String getMimeType() {
            return mimeType;
        }
    }

    /**
     * 比特率模式
     */
    public enum BitrateMode {
        CBR(MediaCodecInfo.EncoderCapabilities.BITRATE_MODE_CBR),
        VBR(MediaCodecInfo.EncoderCapabilities.BITRATE_MODE_VBR),
        CQ(MediaCodecInfo.EncoderCapabilities.BITRATE_MODE_CQ);

        private final int value;

        BitrateMode(int value) {
            this.value = value;
        }

        public int getValue() {
            return value;
        }
    }

}
