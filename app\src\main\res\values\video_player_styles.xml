<?xml version="1.0" encoding="utf-8"?>
<resources>
    
    <!-- 视频控制按钮样式 -->
    <style name="VideoControlButton" parent="Widget.AppCompat.Button">
        <item name="android:background">@drawable/tp_video_button_background</item>
        <item name="android:textColor">#FFFFFF</item>
        <item name="android:textSize">12sp</item>
        <item name="android:minWidth">0dp</item>
        <item name="android:minHeight">0dp</item>
        <item name="android:padding">8dp</item>
    </style>

    <!-- 倍速按钮样式 -->
    <style name="VideoSpeedButton" parent="Widget.AppCompat.Button">
        <item name="android:background">@drawable/tp_speed_item_background</item>
        <item name="android:textColor">#FFFFFF</item>
        <item name="android:textSize">10sp</item>
        <item name="android:minWidth">0dp</item>
        <item name="android:minHeight">0dp</item>
        <item name="android:padding">4dp</item>
    </style>

</resources>
