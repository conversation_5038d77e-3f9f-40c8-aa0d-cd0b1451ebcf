package com.android.rockchip.camera2.integrated.settings.adapters;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.android.rockchip.camera2.integrated.settings.models.SettingsMenuItem;
import com.android.rockchip.mediacodecnew.R;

import java.util.List;

/**
 * 设置菜单适配器
 * 用于显示左侧导航菜单列表
 */
public class SettingsMenuAdapter extends RecyclerView.Adapter<SettingsMenuAdapter.MenuViewHolder> {
    
    private final List<SettingsMenuItem> menuItems;
    private final OnMenuItemClickListener clickListener;
    
    public interface OnMenuItemClickListener {
        void onMenuItemClick(SettingsMenuItem item);
    }
    
    public SettingsMenuAdapter(List<SettingsMenuItem> menuItems, OnMenuItemClickListener clickListener) {
        this.menuItems = menuItems;
        this.clickListener = clickListener;
    }
    
    @NonNull
    @Override
    public MenuViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(parent.getContext())
                .inflate(R.layout.item_settings_menu, parent, false);
        return new MenuViewHolder(view);
    }
    
    @Override
    public void onBindViewHolder(@NonNull MenuViewHolder holder, int position) {
        SettingsMenuItem item = menuItems.get(position);
        holder.bind(item, clickListener);
    }
    
    @Override
    public int getItemCount() {
        return menuItems.size();
    }
    
    /**
     * 菜单项ViewHolder
     */
    static class MenuViewHolder extends RecyclerView.ViewHolder {
        
        private final ImageView menuIcon;
        private final TextView menuTitle;
        private final ImageView menuArrow;
        private final View itemView;
        
        public MenuViewHolder(@NonNull View itemView) {
            super(itemView);
            this.itemView = itemView;
            menuIcon = itemView.findViewById(R.id.menu_icon);
            menuTitle = itemView.findViewById(R.id.menu_title);
            menuArrow = itemView.findViewById(R.id.menu_arrow);
        }
        
        public void bind(SettingsMenuItem item, OnMenuItemClickListener clickListener) {
            // 设置图标
            menuIcon.setImageResource(item.getIconResId());
            
            // 设置标题
            menuTitle.setText(item.getTitle());
            
            // 设置选中状态的视觉效果
            if (item.isSelected()) {
                itemView.setBackgroundColor(itemView.getContext().getResources().getColor(android.R.color.holo_blue_light, null));
                menuTitle.setTextColor(itemView.getContext().getResources().getColor(android.R.color.white, null));
                menuIcon.setColorFilter(itemView.getContext().getResources().getColor(android.R.color.white, null));
                menuArrow.setColorFilter(itemView.getContext().getResources().getColor(android.R.color.white, null));
            } else {
                itemView.setBackgroundColor(itemView.getContext().getResources().getColor(android.R.color.transparent, null));
                menuTitle.setTextColor(itemView.getContext().getResources().getColor(android.R.color.black, null));
                menuIcon.setColorFilter(itemView.getContext().getResources().getColor(android.R.color.darker_gray, null));
                menuArrow.setColorFilter(itemView.getContext().getResources().getColor(android.R.color.darker_gray, null));
            }
            
            // 设置点击事件
            itemView.setOnClickListener(v -> {
                if (clickListener != null) {
                    clickListener.onMenuItemClick(item);
                }
            });
        }
    }
}
