<?xml version="1.0" encoding="utf-8"?>
<!-- 优化的进度条滑块样式 - 与轨道协调的设计 -->
<selector xmlns:android="http://schemas.android.com/apk/res/android">

    <!-- 按下/拖拽状态 - 放大效果 -->
    <item android:state_pressed="true">
        <shape android:shape="oval">
            <solid android:color="#FFFFFFFF" />
            <size android:width="24dp" android:height="24dp" />
        </shape>
    </item>

    <!-- 获得焦点状态 -->
    <item android:state_focused="true">
        <shape android:shape="oval">
            <solid android:color="#FFFFFFFF" />
            <size android:width="22dp" android:height="22dp" />
        </shape>
    </item>

    <!-- 正常状态 -->
    <item>
        <shape android:shape="oval">
            <solid android:color="#FFFFFFFF" />
            <size android:width="20dp" android:height="20dp" />
        </shape>
    </item>

</selector>
