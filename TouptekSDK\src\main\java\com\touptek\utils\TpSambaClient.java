package com.touptek.utils;

import android.content.Context;
import android.content.SharedPreferences;
import android.os.AsyncTask;
import android.util.Log;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.net.MalformedURLException;
import java.util.ArrayList;
import java.util.List;

import jcifs.CIFSContext;
import jcifs.context.SingletonContext;
import jcifs.smb.NtlmPasswordAuthenticator;
import jcifs.smb.SmbException;
import jcifs.smb.SmbFile;
import jcifs.smb.SmbFileOutputStream;

/**
 * SambaUploader类用于连接Samba服务器并上传图片和视频。
 * <p>
 * 此类提供了以下功能：
 * <ul>
 *   <li>保存和加载Samba连接设置</li>
 *   <li>测试Samba连接</li>
 *   <li>将本地图片上传到Samba服务器</li>
 *   <li>将本地视频上传到Samba服务器</li>
 *   <li>提供上传状态回调</li>
 * </ul>
 * 
 * <h2>项目配置</h2>
 * 
 * <h3>1. build.gradle配置</h3>
 * <p>需要在app/build.gradle文件的dependencies部分添加JCIFS依赖：</p>
 * <pre>{@code
 * dependencies {
 *     // 现有依赖...
 *     
 *     // JCIFS库，用于Samba文件传输
 *     implementation 'eu.agno3.jcifs:jcifs-ng:2.1.9'
 * }
 * }</pre>
 * 
 * <h3>2. AndroidManifest.xml配置</h3>
 * <p>需要在AndroidManifest.xml中添加以下网络权限：</p>
 * <pre>{@code
 * <!-- 网络权限，用于Samba文件传输 -->
 * <uses-permission android:name="android.permission.INTERNET" />
 * <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
 * <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
 * }</pre>
 *
 * <h2>使用方法</h2>
 * 
 * <h3>1. 初始化</h3>
 * <pre>{@code
 * // 创建SambaUploader实例
 * TpSambaClient sambaUploader = new TpSambaClient(context);
 * }</pre>
 * 
 * <h3>2. 配置连接参数</h3>
 * <pre>{@code
 * // 设置Samba连接参数
 * sambaUploader.setConnectionParams(
 *     "*************",  // 服务器IP
 *     "username",        // 用户名（留空表示匿名访问）
 *     "password",        // 密码（留空表示匿名访问）
 *     "share",           // 共享名称
 *     "/photos",         // 远程路径
 *     true               // 是否启用上传
 * );
 * 
 * // 或者单独设置是否启用
 * sambaUploader.setEnabled(true);
 *
 * // 使用配置对象方式（推荐）
 * TpSambaClient.SMBConfig config = new TpSambaClient.SMBConfig(
 *     "*************",   // 服务器IP
 *     "username",        // 用户名
 *     "password",        // 密码
 *     "share",           // 共享名称
 *     "/photos",         // 远程路径
 *     true               // 是否启用
 * );
 * sambaUploader.setConnectionParams(config);
 *
 * // 获取当前配置
 * TpSambaClient.SMBConfig currentConfig = sambaUploader.getConnectionParams();
 * String serverIp = currentConfig.getServerIp();
 * }</pre>
 *
 * <h3>3. 测试连接</h3>
 * <pre>{@code
 * sambaUploader.testConnection(new TpSambaClient.UploadListener() {
 *     @Override
 *     public void onUploadSuccess(String remoteFilePath) {
 *         // 连接成功
 *         Toast.makeText(context, "连接成功!", Toast.LENGTH_SHORT).show();
 *     }
 *     
 *     @Override
 *     public void onUploadFailed(String errorMessage) {
 *         // 连接失败
 *         Toast.makeText(context, "连接失败: " + errorMessage, Toast.LENGTH_SHORT).show();
 *     }
 * });
 * }</pre>
 * 
 * <h3>4. 上传文件</h3>
 * <pre>{@code
 * // 上传图片文件
 * String imagePath = "/storage/emulated/0/Pictures/image.jpg";
 * sambaUploader.uploadFile(imagePath, new TpSambaClient.UploadListener() {
 *     @Override
 *     public void onUploadSuccess(String remoteFilePath) {
 *         Log.d("TpSambaClient", "文件上传成功: " + remoteFilePath);
 *     }
 *
 *     @Override
 *     public void onUploadFailed(String errorMessage) {
 *         Log.e("TpSambaClient", "文件上传失败: " + errorMessage);
 *     }
 * });
 *
 * // 上传视频文件
 * String videoPath = "/storage/emulated/0/Movies/video.mp4";
 * sambaUploader.uploadFile(videoPath, new TpSambaClient.UploadListener() {
 *     @Override
 *     public void onUploadSuccess(String remoteFilePath) {
 *         Log.d("TpSambaClient", "视频上传成功: " + remoteFilePath);
 *     }
 *
 *     @Override
 *     public void onUploadFailed(String errorMessage) {
 *         Log.e("TpSambaClient", "视频上传失败: " + errorMessage);
 *     }
 * });
 *
 * // 上传文件并指定远程文件名
 * String localPath = "/storage/emulated/0/Pictures/photo.jpg";
 * String remoteName = "renamed_photo.jpg";
 * sambaUploader.uploadFile(localPath, remoteName, new TpSambaClient.UploadListener() {
 *     @Override
 *     public void onUploadSuccess(String remoteFilePath) {
 *         Log.d("TpSambaClient", "文件上传成功: " + remoteFilePath);
 *     }
 *
 *     @Override
 *     public void onUploadFailed(String errorMessage) {
 *         Log.e("TpSambaClient", "文件上传失败: " + errorMessage);
 *     }
 * });
 * }</pre>
 * 
 * <h3>5. 获取远程目录列表</h3>
 * <pre>{@code
 * sambaUploader.getRemoteDirectories(new TpSambaClient.DirectoryListListener() {
 *     @Override
 *     public void onDirectoriesLoaded(List<String> directories) {
 *         // 处理获取到的目录列表
 *         for (String dir : directories) {
 *             Log.d("TpSambaClient", "发现目录: " + dir);
 *         }
 *     }
 *     
 *     @Override
 *     public void onDirectoryLoadFailed(String errorMessage) {
 *         Log.e("TpSambaClient", "获取目录失败: " + errorMessage);
 *     }
 * });
 * }</pre>
 * 
 * <h3>6. 与CaptureImageHelper集成</h3>
 * <pre>{@code
 * // 创建CaptureImageHelper并设置回调
 * TpCaptureImage captureImageHelper = TpCaptureImage.builder(new Size(3840, 2160))
 *     .onImageSaved(filePath -> {
 *         // 图片已保存到本地
 *         runOnUiThread(() -> Toast.makeText(this, "图片已保存: " + filePath, Toast.LENGTH_SHORT).show());
 *         
 *         // 在回调中处理SMB上传
 *         if (sambaUploader != null && sambaUploader.isEnabled()) {
 *             Log.d(TAG, "开始上传图片到SMB服务器: " + filePath);
 *             sambaUploader.uploadFile(filePath, new TpSambaClient.UploadListener() {
 *                 @Override
 *                 public void onUploadSuccess(String remoteFilePath) {
 *                     Log.d(TAG, "图片上传成功: " + remoteFilePath);
 *                 }
 *
 *                 @Override
 *                 public void onUploadFailed(String errorMessage) {
 *                     Log.e(TAG, "图片上传失败: " + errorMessage);
 *                 }
 *             });
 *         }
 *     })
 *     .onError(errorMessage -> {
 *         // 显示错误提示
 *         runOnUiThread(() -> Toast.makeText(this, "抓图失败: " + errorMessage, Toast.LENGTH_SHORT).show());
 *     })
 *     .build();
 * }</pre>
 * 
 * <h3>7. 在Activity中完整实现</h3>
 * <pre>{@code
 * public class MainActivity extends AppCompatActivity {
 *     private TpSambaClient sambaUploader;
 *     private TpCaptureImage captureImageHelper;
 *     
 *     @Override
 *     protected void onCreate(Bundle savedInstanceState) {
 *         super.onCreate(savedInstanceState);
 *         
 *         // 初始化SMB上传器
 *         sambaUploader = new TpSambaClient(this);
 *         
 *         // 初始化抓图助手
 *         captureImageHelper = TpCaptureImage.builder(new Size(3840, 2160))
 *             .onImageSaved(filePath -> {
 *                 // 显示Toast提示
 *                 runOnUiThread(() -> Toast.makeText(this, "图片已保存: " + filePath, Toast.LENGTH_SHORT).show());
 *                 
 *                 // 在回调中处理SMB上传
 *                 if (sambaUploader != null && sambaUploader.isEnabled()) {
 *                     Log.d(TAG, "开始上传图片到SMB服务器: " + filePath);
 *                     sambaUploader.uploadFile(filePath, new TpSambaClient.UploadListener() {
 *                         @Override
 *                         public void onUploadSuccess(String remoteFilePath) {
 *                             Log.d(TAG, "图片上传成功: " + remoteFilePath);
 *                         }
 *                         
 *                         @Override
 *                         public void onUploadFailed(String errorMessage) {
 *                             Log.e(TAG, "图片上传失败: " + errorMessage);
 *                         }
 *                     });
 *                 }
 *             })
 *             .onError(errorMessage -> {
 *                 // 显示错误提示
 *                 runOnUiThread(() -> Toast.makeText(this, "抓图失败: " + errorMessage, Toast.LENGTH_SHORT).show());
 *             })
 *             .build();
 *             
 *         // 设置SMB设置按钮点击事件
 *         findViewById(R.id.btnSmbSettings).setOnClickListener(v -> {
 *             showSMBSettingsDialog();
 *         });
 *         
 *         // 设置拍照按钮点击事件
 *         findViewById(R.id.btnCapture).setOnClickListener(v -> {
 *             captureImage();
 *         });
 *     }
 *     
 *     // 显示SMB设置对话框
 *     private void showSMBSettingsDialog() {
 *         if (sambaUploader != null) {
 *             SMBSettingsDialog dialog = new SMBSettingsDialog(this, sambaUploader);
 *             dialog.show();
 *         }
 *     }
 *     
 *     // 抓图方法
 *     private void captureImage() {
 *         Size imageSize = new Size(3840, 2160);
 *         String outputPath = TpFileManager.createImagePath(this, "prefix", true, "jpg");
 *         captureImageHelper.requestCapture(imageSize, outputPath);
 *     }
 * }
 * }</pre>
 * 
 * <h3>9. API说明</h3>
 * 
 * <h4>接口和回调</h4>
 * <ul>
 *   <li><code>UploadListener</code> - 上传结果回调接口，包含成功和失败的回调方法</li>
 *   <li><code>DirectoryListListener</code> - 目录列表回调接口，包含成功获取目录列表和失败的回调方法</li>
 * </ul>
 * 
 * <h4>主要方法</h4>
 * <ul>
 *   <li><code>TpSambaClient(Context context)</code> - 构造函数，创建上传器实例</li>
 *   <li><code>setConnectionParams(String serverIp, String username, String password, String shareName, String remotePath, boolean enabled)</code> - 设置连接参数</li>
 *   <li><code>setConnectionParams(SMBConfig config)</code> - 使用配置对象设置连接参数</li>
 *   <li><code>getConnectionParams()</code> - 获取当前连接配置</li>
 *   <li><code>isEnabled()</code> - 检查是否启用了Samba上传</li>
 *   <li><code>setEnabled(boolean enabled)</code> - 设置是否启用Samba上传</li>
 *   <li><code>testConnection(UploadListener callback)</code> - 测试Samba服务器连接</li>
 *   <li><code>getRemoteDirectories(DirectoryListListener callback)</code> - 获取远程目录列表</li>
 *   <li><code>uploadFile(String localFilePath, UploadListener callback)</code> - 上传文件到Samba服务器</li>
 *   <li><code>uploadFile(String localFilePath, String remoteFileName, UploadListener callback)</code> - 上传文件并指定远程文件名</li>
 * </ul>
 */
public class TpSambaClient {
    private static final String TAG = "TpSambaClient";
    private static final String PREFS_NAME = "SambaUploaderPrefs";
    
    /**
     * 上传结果回调接口
     */
    public interface UploadListener {
        void onUploadSuccess(String remoteFilePath);
        void onUploadFailed(String errorMessage);
    }
    
    /**
     * 目录列表回调接口
     */
    public interface DirectoryListListener {
        void onDirectoriesLoaded(List<String> directories);
        void onDirectoryLoadFailed(String errorMessage);
    }

    /**
     * SMB连接配置类
     */
    public static class SMBConfig {
        private final String serverIp;
        private final String username;
        private final String password;
        private final String shareName;
        private final String remotePath;
        private final boolean enabled;

        public SMBConfig(String serverIp, String username, String password,
                        String shareName, String remotePath, boolean enabled) {
            this.serverIp = serverIp;
            this.username = username;
            this.password = password;
            this.shareName = shareName;
            this.remotePath = remotePath;
            this.enabled = enabled;
        }

        public String getServerIp() { return serverIp; }
        public String getUsername() { return username; }
        public String getShareName() { return shareName; }
        public String getRemotePath() { return remotePath; }
        public boolean isEnabled() { return enabled; }

        // 注意：不提供getPassword()方法以提升安全性
    }
    
    // Samba连接参数
    private String serverIp;
    private String username;
    private String password;
    private String shareName;
    private String remotePath;
    private boolean enabled = false;
    
    private Context context;
    
    /**
     * 构造函数
     * @param context 应用上下文
     */
    public TpSambaClient(Context context) {
        this.context = context;
        loadSettings();
    }
    
    /**
     * 从SharedPreferences加载设置
     */
    private void loadSettings() {
        SharedPreferences prefs = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE);
        serverIp = prefs.getString("serverIp", "");
        username = prefs.getString("username", "");
        password = prefs.getString("password", "");
        shareName = prefs.getString("shareName", "");
        remotePath = prefs.getString("remotePath", "/");
        enabled = prefs.getBoolean("enabled", false);
        
        Log.d(TAG, "加载Samba设置: 启用状态=" + enabled + ", 服务器=" + serverIp);
    }
    
    /**
     * 保存设置到SharedPreferences
     */
    private void saveSettings() {
        SharedPreferences prefs = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE);
        SharedPreferences.Editor editor = prefs.edit();
        editor.putString("serverIp", serverIp);
        editor.putString("username", username);
        editor.putString("password", password);
        editor.putString("shareName", shareName);
        editor.putString("remotePath", remotePath);
        editor.putBoolean("enabled", enabled);
        editor.apply();
        
        Log.d(TAG, "保存Samba设置: 启用状态=" + enabled + ", 服务器=" + serverIp);
    }
    
    /**
     * 设置Samba连接参数
     *
     * @param serverIp Samba服务器的IP地址
     * @param username 登录用户名，留空表示匿名访问
     * @param password 登录密码，留空表示匿名访问
     * @param shareName Samba共享名称
     * @param remotePath 服务器上的路径
     * @param enabled 是否启用Samba上传功能
     */
    public void setConnectionParams(String serverIp, String username, String password,
                                   String shareName, String remotePath, boolean enabled) {
        this.serverIp = serverIp;
        this.username = username;
        this.password = password;
        this.shareName = shareName;
        this.remotePath = remotePath;
        this.enabled = enabled;
        saveSettings();
    }

    /**
     * 设置Samba连接参数（使用配置对象）
     *
     * @param config SMB连接配置对象
     */
    public void setConnectionParams(SMBConfig config) {
        this.serverIp = config.getServerIp();
        this.username = config.getUsername();
        this.password = config.password; // 直接访问私有字段
        this.shareName = config.getShareName();
        this.remotePath = config.getRemotePath();
        this.enabled = config.isEnabled();
        saveSettings();
    }

    /**
     * 获取当前连接配置
     *
     * @return SMB连接配置对象
     */
    public SMBConfig getConnectionParams() {
        return new SMBConfig(serverIp, username, password, shareName, remotePath, enabled);
    }
    

    
    /**
     * 检查是否启用了Samba上传
     */
    public boolean isEnabled() {
        return enabled;
    }
    
    /**
     * 设置是否启用Samba上传
     */
    public void setEnabled(boolean enabled) {
        this.enabled = enabled;
        saveSettings();
    }
    

    
    /**
     * 测试Samba服务器连接
     */
    public void testConnection(UploadListener callback) {
        if (!enabled) {
            if (callback != null) {
                callback.onUploadFailed("Samba上传功能未启用");
            }
            return;
        }
        
        new TestConnectionTask(callback).execute();
    }
    
    /**
     * 获取远程目录列表
     */
    public void getRemoteDirectories(DirectoryListListener callback) {
        // 检查参数
        if (serverIp.isEmpty() || shareName.isEmpty()) {
            callback.onDirectoryLoadFailed("服务器IP或共享名称不能为空");
            return;
        }
        
        new ListDirectoriesTask(callback).execute();
    }
    
    /**
     * 上传文件到Samba服务器（统一方法）
     *
     * @param localFilePath 本地文件路径
     * @param callback 上传结果回调
     */
    public void uploadFile(String localFilePath, UploadListener callback) {
        if (!enabled) {
            Log.d(TAG, "Samba上传功能未启用，跳过上传");
            return;
        }

        Log.d(TAG, "开始上传文件到Samba服务器: " + localFilePath);
        new UploadTask(localFilePath, callback).execute();
    }

    /**
     * 上传文件到Samba服务器（指定远程文件名）
     *
     * @param localFilePath 本地文件路径
     * @param remoteFileName 远程文件名（可选，为null时使用本地文件名）
     * @param callback 上传结果回调
     */
    public void uploadFile(String localFilePath, String remoteFileName, UploadListener callback) {
        if (!enabled) {
            Log.d(TAG, "Samba上传功能未启用，跳过上传");
            return;
        }

        Log.d(TAG, "开始上传文件到Samba服务器: " + localFilePath + " -> " + remoteFileName);
        new UploadTask(localFilePath, remoteFileName, callback).execute();
    }


    
    /**
     * 测试连接的异步任务
     */
    private class TestConnectionTask extends AsyncTask<Void, Void, Boolean> {
        private UploadListener callback;
        private String errorMessage = "";
        
        public TestConnectionTask(UploadListener callback) {
            this.callback = callback;
        }
        
        @Override
        protected Boolean doInBackground(Void... voids) {
            try {
                // 检查参数
                if (serverIp.isEmpty()) {
                    errorMessage = "服务器IP不能为空";
                    return false;
                }
                
                if (shareName.isEmpty()) {
                    errorMessage = "共享名称不能为空";
                    return false;
                }
                
                // 创建CIFS上下文并设置认证信息
                CIFSContext auth;
                if (username.isEmpty() && password.isEmpty()) {
                    // 匿名访问
                    auth = SingletonContext.getInstance().withGuestCrendentials();
                } else {
                    auth = SingletonContext.getInstance().withCredentials(
                            new NtlmPasswordAuthenticator(username, password));
                }
                
                // 构建SMB URL
                String smbUrl = "smb://" + serverIp + "/" + shareName + remotePath;
                if (!remotePath.endsWith("/")) {
                    smbUrl += "/";
                }
                
                // 尝试连接
                SmbFile smbFile = new SmbFile(smbUrl, auth);
                smbFile.exists(); // 如果连接失败会抛出异常
                
                Log.d(TAG, "Samba连接测试成功: " + smbUrl);
                return true;
            } catch (MalformedURLException e) {
                errorMessage = "无效的URL: " + e.getMessage();
                Log.e(TAG, "SMB URL格式错误", e);
            } catch (SmbException e) {
                errorMessage = "SMB错误: " + e.getMessage();
                Log.e(TAG, "SMB连接错误", e);
            } catch (Exception e) {
                errorMessage = "未预期的错误: " + e.getMessage();
                Log.e(TAG, "未知错误", e);
            }
            
            return false;
        }
        
        @Override
        protected void onPostExecute(Boolean success) {
            if (callback != null) {
                if (success) {
                    callback.onUploadSuccess("连接测试成功");
                } else {
                    callback.onUploadFailed(errorMessage);
                }
            }
        }
    }
    
    /**
     * 获取远程目录的异步任务
     */
    private class ListDirectoriesTask extends AsyncTask<Void, Void, List<String>> {
        private DirectoryListListener callback;
        private String errorMessage = "";
        
        public ListDirectoriesTask(DirectoryListListener callback) {
            this.callback = callback;
        }
        
        @Override
        protected List<String> doInBackground(Void... voids) {
            List<String> directories = new ArrayList<>();
            try {
                // 创建CIFS上下文并设置认证信息
                CIFSContext auth;
                if (username.isEmpty() && password.isEmpty()) {
                    // 匿名访问
                    auth = SingletonContext.getInstance().withGuestCrendentials();
                } else {
                    auth = SingletonContext.getInstance().withCredentials(
                            new NtlmPasswordAuthenticator(username, password));
                }
                
                // 构建SMB URL，连接到共享根目录
                String smbUrl = "smb://" + serverIp + "/" + shareName + "/";
                SmbFile smbFile = new SmbFile(smbUrl, auth);
                
                // 检查是否存在和可访问
                if (!smbFile.exists()) {
                    errorMessage = "共享不存在或无法访问";
                    return null;
                }
                
                // 添加根目录
                directories.add("/");
                
                // 获取所有子目录
                SmbFile[] files = smbFile.listFiles();
                if (files != null) {
                    for (SmbFile file : files) {
                        // 只收集目录，不包括文件
                        if (file.isDirectory()) {
                            // 提取目录路径，移除服务器和共享名部分
                            String path = file.getPath();
                            // smb://server/share/dir/ -> /dir/
                            path = path.substring(smbUrl.length() - 1);
                            directories.add(path);
                            
                            // 尝试列出一级子目录
                            try {
                                SmbFile[] subFiles = file.listFiles();
                                if (subFiles != null) {
                                    for (SmbFile subFile : subFiles) {
                                        if (subFile.isDirectory()) {
                                            String subPath = subFile.getPath();
                                            subPath = subPath.substring(smbUrl.length() - 1);
                                            directories.add(subPath);
                                        }
                                    }
                                }
                            } catch (Exception e) {
                                // 子目录访问失败，但继续处理其他目录
                                Log.w(TAG, "无法访问子目录: " + path, e);
                            }
                        }
                    }
                }
                
                Log.d(TAG, "成功获取远程目录列表，共 " + directories.size() + " 个目录");
                return directories;
                
            } catch (MalformedURLException e) {
                errorMessage = "无效的URL: " + e.getMessage();
                Log.e(TAG, "SMB URL格式错误", e);
            } catch (SmbException e) {
                errorMessage = "SMB错误: " + e.getMessage();
                Log.e(TAG, "SMB列目录错误", e);
            } catch (Exception e) {
                errorMessage = "未预期的错误: " + e.getMessage();
                Log.e(TAG, "获取目录列表时发生未知错误", e);
            }
            
            return null;
        }
        
        @Override
        protected void onPostExecute(List<String> directories) {
            if (callback != null) {
                if (directories != null) {
                    callback.onDirectoriesLoaded(directories);
                } else {
                    callback.onDirectoryLoadFailed(errorMessage);
                }
            }
        }
    }
    
    /**
     * 上传文件的异步任务
     */
    private class UploadTask extends AsyncTask<Void, Void, Boolean> {
        private String localFilePath;
        private String remoteFileName;
        private UploadListener callback;
        private String errorMessage = "";
        private String remoteFilePath = "";

        public UploadTask(String localFilePath, UploadListener callback) {
            this.localFilePath = localFilePath;
            this.remoteFileName = null; // 使用默认文件名
            this.callback = callback;
        }

        public UploadTask(String localFilePath, String remoteFileName, UploadListener callback) {
            this.localFilePath = localFilePath;
            this.remoteFileName = remoteFileName;
            this.callback = callback;
        }
        
        @Override
        protected Boolean doInBackground(Void... voids) {
            try {
                // 检查本地文件是否存在
                File localFile = new File(localFilePath);
                if (!localFile.exists()) {
                    errorMessage = "本地文件不存在: " + localFilePath;
                    return false;
                }
                
                // 创建CIFS上下文并设置认证信息
                CIFSContext auth;
                if (username.isEmpty() && password.isEmpty()) {
                    // 匿名访问
                    auth = SingletonContext.getInstance().withGuestCrendentials();
                } else {
                    auth = SingletonContext.getInstance().withCredentials(
                            new NtlmPasswordAuthenticator(username, password));
                }
                
                // 构建SMB URL
                String fileName = (remoteFileName != null) ? remoteFileName : localFile.getName();
                String smbUrl = "smb://" + serverIp + "/" + shareName + remotePath;
                if (!remotePath.endsWith("/")) {
                    smbUrl += "/";
                }

                remoteFilePath = smbUrl + fileName;
                Log.d(TAG, "准备上传文件到: " + remoteFilePath);
                
                // 创建SMB文件并写入内容
                SmbFile smbFile = new SmbFile(remoteFilePath, auth);
                SmbFileOutputStream outputStream = new SmbFileOutputStream(smbFile);
                
                // 读取本地文件并写入SMB文件
                FileInputStream inputStream = new FileInputStream(localFile);
                byte[] buffer = new byte[8192];
                int bytesRead;
                long totalBytes = 0;
                
                while ((bytesRead = inputStream.read(buffer)) != -1) {
                    outputStream.write(buffer, 0, bytesRead);
                    totalBytes += bytesRead;
                }
                
                inputStream.close();
                outputStream.close();
                
                Log.d(TAG, "文件上传成功: " + remoteFilePath + ", 大小: " + totalBytes + " bytes");
                return true;
            } catch (MalformedURLException e) {
                errorMessage = "无效的URL: " + e.getMessage();
                Log.e(TAG, "SMB URL格式错误", e);
            } catch (IOException e) {
                errorMessage = "I/O错误: " + e.getMessage();
                Log.e(TAG, "SMB文件写入错误", e);
            } catch (Exception e) {
                errorMessage = "未预期的错误: " + e.getMessage();
                Log.e(TAG, "未知错误", e);
            }
            
            return false;
        }
        
        @Override
        protected void onPostExecute(Boolean success) {
            if (callback != null) {
                if (success) {
                    callback.onUploadSuccess(remoteFilePath);
                } else {
                    callback.onUploadFailed(errorMessage);
                }
            }
        }
    }
} 