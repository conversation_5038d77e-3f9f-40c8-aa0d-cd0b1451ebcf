<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:background="#AA333333"
    android:padding="12dp"
    android:elevation="4dp"
    android:layout_margin="8dp">

    <!-- 标题栏 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center_vertical">

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="相机设置"
            android:textColor="#FFFFFF"
            android:textSize="16sp"
            android:textStyle="bold" />

        <ImageButton
            android:id="@+id/btn_close_popup"
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:background="@android:drawable/ic_menu_close_clear_cancel"
            android:backgroundTint="#FFFFFF" />
    </LinearLayout>

    <!-- 分隔线 -->
    <View
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:background="#44FFFFFF"
        android:layout_marginTop="8dp"
        android:layout_marginBottom="8dp" />

    <!-- TV模式行 - 新增加 -->
    <LinearLayout
        android:id="@+id/menu_tv_mode"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center_vertical"
        android:padding="8dp">

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="TV模式"
            android:textColor="#FFFFFF"
            android:textSize="14sp" />

        <Button
            android:id="@+id/btn_switch_mode"
            android:layout_width="500dp"
            android:layout_height="36dp"
            android:background="#44888888"
            android:text="切换到TV模式"
            android:textColor="#FFFFFF"
            android:textSize="14sp"/>
    </LinearLayout>

    <!-- 分隔线 -->
    <View
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:background="#22FFFFFF"
        android:layout_marginTop="4dp"
        android:layout_marginBottom="4dp" />

    <!-- ROI模式行 -->
    <LinearLayout
        android:id="@+id/menu_roi_mode"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center_vertical"
        android:padding="8dp">

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="ROI模式"
            android:textColor="#FFFFFF"
            android:textSize="14sp" />

        <Switch
            android:id="@+id/switch_roi_mode"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content" />
    </LinearLayout>

    <!-- 分隔线 -->
    <View
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:background="#22FFFFFF"
        android:layout_marginTop="4dp"
        android:layout_marginBottom="4dp" />

    <!-- 水平翻转行 -->
    <LinearLayout
        android:id="@+id/menu_horizontal_flip"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center_vertical"
        android:padding="8dp">

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="水平翻转"
            android:textColor="#FFFFFF"
            android:textSize="14sp" />

        <Switch
            android:id="@+id/switch_horizontal_flip"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content" />
    </LinearLayout>

    <!-- 分隔线 -->
    <View
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:background="#22FFFFFF"
        android:layout_marginTop="4dp"
        android:layout_marginBottom="4dp" />

    <!-- 垂直翻转行 -->
    <LinearLayout
        android:id="@+id/menu_vertical_flip"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center_vertical"
        android:padding="8dp">

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="垂直翻转"
            android:textColor="#FFFFFF"
            android:textSize="14sp" />

        <Switch
            android:id="@+id/switch_vertical_flip"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content" />
    </LinearLayout>

    <!-- 分隔线 -->
    <View
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:background="#22FFFFFF"
        android:layout_marginTop="4dp"
        android:layout_marginBottom="4dp" />

    <!-- 网络设置行 -->
    <LinearLayout
        android:id="@+id/menu_network_settings"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center_vertical"
        android:padding="8dp">

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="网络设置"
            android:textColor="#FFFFFF"
            android:textSize="14sp" />

        <ImageButton
            android:id="@+id/btn_open_network"
            android:layout_width="500dp"
            android:layout_height="36dp"
            android:background="#44888888"
            android:src="@android:drawable/ic_menu_preferences"
            android:scaleType="centerInside"
            app:tint="#FFFFFF" />
    </LinearLayout>

    <!-- 分隔线 -->
    <View
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:background="#22FFFFFF"
        android:layout_marginTop="4dp"
        android:layout_marginBottom="4dp" />

    <!-- SMB设置行 -->
    <LinearLayout
        android:id="@+id/menu_smb_settings"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center_vertical"
        android:padding="8dp">

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="SMB设置"
            android:textColor="#FFFFFF"
            android:textSize="14sp" />

        <ImageButton
            android:id="@+id/btn_open_smb"
            android:layout_width="500dp"
            android:layout_height="36dp"
            android:background="#44888888"
            android:src="@android:drawable/ic_menu_save"
            android:scaleType="centerInside"
            app:tint="#FFFFFF" />
    </LinearLayout>

    <!-- 分隔线 -->
    <View
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:background="#22FFFFFF"
        android:layout_marginTop="4dp"
        android:layout_marginBottom="4dp" />

    <!-- 图片格式设置行 -->
    <LinearLayout
        android:id="@+id/menu_image_format"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="8dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical">

            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="图片格式"
                android:textColor="#FFFFFF"
                android:textSize="14sp" />

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center_vertical"
                android:background="#44888888">

                <Spinner
                    android:id="@+id/spinner_image_format"
                    android:layout_width="180dp"
                    android:layout_height="36dp"
                    android:background="@null"
                    android:popupBackground="#AA333333"
                    android:spinnerMode="dropdown" />

                <ImageView
                    android:layout_width="24dp"
                    android:layout_height="24dp"
                    android:src="@android:drawable/arrow_down_float"
                    app:tint="#FFFFFF"
                    android:layout_marginEnd="4dp"/>
            </LinearLayout>
        </LinearLayout>

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="选择抓图保存的图片格式"
            android:textColor="#AAAAAA"
            android:textSize="12sp"
            android:layout_marginTop="4dp"/>
    </LinearLayout>

    <!-- 分隔线 -->
    <View
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:background="#22FFFFFF"
        android:layout_marginTop="4dp"
        android:layout_marginBottom="4dp" />

    <!-- 场景数值行 -->
    <LinearLayout
        android:id="@+id/menu_scene_mode"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center_vertical"
        android:padding="8dp">

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="场景值"
            android:textColor="#FFFFFF"
            android:textSize="14sp" />

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical">

            <EditText
                android:id="@+id/input_scene_value"
                android:layout_width="200dp"
                android:layout_height="36dp"
                android:background="#44888888"
                android:inputType="number"
                android:maxLength="3"
                android:gravity="center"
                android:textColor="#FFFFFF"
                android:textSize="20sp"
                android:hint="0-100"
                android:textColorHint="#88FFFFFF"
                android:paddingStart="8dp"
                android:paddingEnd="8dp" />

            <ImageButton
                android:id="@+id/btn_apply_scene"
                android:layout_width="100dp"
                android:layout_height="36dp"
                android:layout_marginStart="8dp"
                android:background="#44888888"
                android:src="@android:drawable/ic_menu_send"
                android:scaleType="centerInside"
                app:tint="#FFFFFF" />
        </LinearLayout>
    </LinearLayout>
</LinearLayout>