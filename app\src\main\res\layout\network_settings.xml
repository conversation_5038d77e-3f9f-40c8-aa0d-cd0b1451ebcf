<?xml version="1.0" encoding="utf-8"?>
<LinearLayout 
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:padding="16dp"
    android:background="#AA333333">

    <!-- 标题栏 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center_vertical">

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="网络设置"
            android:textColor="#FFFFFF"
            android:textSize="16sp"
            android:textStyle="bold" />

        <ImageButton
            android:id="@+id/btn_close_network"
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:background="@android:drawable/ic_menu_close_clear_cancel"
            android:backgroundTint="#FFFFFF" />
    </LinearLayout>

    <!-- 分隔线 -->
    <View
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:background="#44FFFFFF"
        android:layout_marginTop="8dp"
        android:layout_marginBottom="8dp" />

    <!-- 以太网状态 -->
    <TextView
        android:id="@+id/ethernet_status_text"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:textColor="#FFFFFF"
        android:textSize="14sp"
        android:padding="8dp"
        android:text="以太网状态：未连接" />

    <!-- 分隔线 -->
    <View
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:background="#22FFFFFF"
        android:layout_marginTop="4dp"
        android:layout_marginBottom="4dp" />

    <!-- WiFi状态 -->
    <TextView
        android:id="@+id/wifi_status_text"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:textColor="#FFFFFF"
        android:textSize="14sp"
        android:padding="8dp"
        android:text="WIFI状态：未连接" />

    <!-- WiFi按钮 -->
    <Button
        android:id="@+id/wifi_button"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        android:text="连接WIFI"
        android:background="#44888888"
        android:textColor="#FFFFFF" />

    <!-- 分隔线 -->
    <View
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:background="#22FFFFFF"
        android:layout_marginTop="12dp"
        android:layout_marginBottom="4dp" />

    <!-- 热点状态 -->
    <TextView
        android:id="@+id/hotspot_status_text"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:textColor="#FFFFFF"
        android:textSize="14sp"
        android:padding="8dp"
        android:text="热点状态：未开启" />

    <!-- 热点按钮 -->
    <Button
        android:id="@+id/hotspot_button"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        android:text="开启热点"
        android:background="#44888888"
        android:textColor="#FFFFFF" />

    <!-- 分隔线 -->
    <View
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:background="#22FFFFFF"
        android:layout_marginTop="12dp"
        android:layout_marginBottom="4dp" />
    
    <!-- RTSP推流控制部分 -->
    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:textColor="#FFFFFF"
        android:textSize="14sp"
        android:textStyle="bold"
        android:padding="8dp"
        android:text="RTSP推流控制" />
    
    <!-- RTSP状态 -->
    <TextView
        android:id="@+id/rtsp_status_text"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:textColor="#FFFFFF"
        android:textSize="14sp"
        android:padding="8dp"
        android:text="RTSP状态：未启动" />
    
    <!-- RTSP URL -->
    <TextView
        android:id="@+id/rtsp_url_text"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:textColor="#FFFFFF"
        android:textSize="14sp"
        android:padding="8dp"
        android:text="RTSP URL：" />
    
    <!-- 网络接口选择 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center_vertical"
        android:padding="8dp">
        
        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textColor="#FFFFFF"
            android:textSize="14sp"
            android:text="选择网络接口：" />
        
        <Spinner
            android:id="@+id/network_interface_spinner"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="#44FFFFFF"
            android:popupBackground="#AA333333" />
    </LinearLayout>
    
    <!-- 流类型选择 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center_vertical"
        android:padding="8dp">
        
        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textColor="#FFFFFF"
            android:textSize="14sp"
            android:text="推流类型：" />
        
        <RadioGroup
            android:id="@+id/stream_type_radio_group"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal">
            
            <RadioButton
                android:id="@+id/radio_camera_stream"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="摄像头流"
                android:textColor="#FFFFFF"
                android:checked="true"
                android:buttonTint="#FFFFFF" />
            
            <RadioButton
                android:id="@+id/radio_screen_stream"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="屏幕流"
                android:textColor="#FFFFFF"
                android:layout_marginStart="16dp"
                android:buttonTint="#FFFFFF" />
        </RadioGroup>
    </LinearLayout>
    
    <!-- RTSP推流控制按钮 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:padding="8dp">
        
        <Button
            android:id="@+id/start_rtsp_button"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="启动推流"
            android:background="#446688CC"
            android:textColor="#FFFFFF"
            android:layout_marginEnd="8dp" />
        
        <Button
            android:id="@+id/stop_rtsp_button"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="停止推流"
            android:background="#44CC6666"
            android:textColor="#FFFFFF"
            android:layout_marginStart="8dp"
            android:enabled="false" />
    </LinearLayout>

    <!-- 分隔线 -->
    <View
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:background="#22FFFFFF"
        android:layout_marginTop="12dp"
        android:layout_marginBottom="4dp" />

    <!-- 网络信息显示区域 -->
    <TextView
        android:id="@+id/network_info_text"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:textColor="#FFFFFF"
        android:textSize="14sp"
        android:padding="8dp"
        android:minHeight="80dp"
        android:background="#22FFFFFF"
        android:layout_marginTop="8dp"
        android:layout_marginBottom="8dp" />

</LinearLayout> 