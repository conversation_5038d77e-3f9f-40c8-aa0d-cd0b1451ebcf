package com.android.rockchip.camera2.integrated.settings.fragments;

import android.content.Intent;
import android.os.Bundle;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.AdapterView;
import android.widget.ArrayAdapter;
import android.widget.Button;
import android.widget.RadioButton;
import android.widget.RadioGroup;
import android.widget.Spinner;
import android.widget.TextView;
import android.widget.Toast;

import androidx.activity.result.ActivityResultLauncher;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;


import com.touptek.utils.TpNetworkMonitor;
import com.touptek.video.TpVideoSystem;
import com.android.rockchip.mediacodecnew.R;

import java.util.ArrayList;
import java.util.List;

/**
 * ToupTek 网络设置Fragment
 * 迁移自NetworkSettingsDialog的所有功能
 */
public class TpNetworkSettingsFragment extends Fragment implements TpNetworkMonitor.NetworkStateListener {
    
    private static final String TAG = "TpNetworkSettingsFragment";
    
    // 网络设置相关控件
    private TextView ethernetStatusText;
    private TextView wifiStatusText;
    private TextView hotspotStatusText;
    private TextView networkInfoText;
    private Button wifiButton;
    private Button hotspotButton;
    
    // RTSP推流控制相关控件
    private TextView rtspStatusText;
    private TextView rtspUrlText;
    private Spinner networkInterfaceSpinner;
    private RadioGroup streamTypeRadioGroup;
    private RadioButton cameraStreamRadio;
    private RadioButton screenStreamRadio;
    private Button startRtspButton;
    private Button stopRtspButton;
    
    // 依赖对象
    private TpVideoSystem videoSystem;
    private TpNetworkMonitor tpNetworkMonitor;
    private ActivityResultLauncher<Intent> wifiPanelLauncher;
    
    // 状态变量
    private boolean isWifiEnabled = false;
    private List<TpNetworkMonitor.NetworkInterfaceInfo> networkInterfaces = new ArrayList<>();
    private String selectedNetworkInterface = null;
    private boolean isRtspStreaming = false;
    
    /**
     * 创建Fragment实例的工厂方法
     */
    public static TpNetworkSettingsFragment newInstance(TpVideoSystem videoSystem,
                                                       ActivityResultLauncher<Intent> wifiPanelLauncher) {
        TpNetworkSettingsFragment fragment = new TpNetworkSettingsFragment();
        fragment.videoSystem = videoSystem;
        fragment.wifiPanelLauncher = wifiPanelLauncher;
        return fragment;
    }
    
    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        Log.d(TAG, "TpNetworkSettingsFragment onCreate");
    }
    
    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, 
                           @Nullable Bundle savedInstanceState) {
        View view = inflater.inflate(R.layout.fragment_tp_network_settings, container, false);
        
        // 初始化视图
        initViews(view);
        
        // 初始化网络管理器
        initNetworkManager();
        
        // 设置事件监听器
        setupEventListeners();
        
        // 初始化RTSP控制
        initRtspControls();
        
        Log.d(TAG, "TpNetworkSettingsFragment onCreateView 完成");
        return view;
    }
    
    /**
     * 初始化视图组件
     */
    private void initViews(View view) {
        // 网络状态显示
        ethernetStatusText = view.findViewById(R.id.ethernet_status_text);
        wifiStatusText = view.findViewById(R.id.wifi_status_text);
        hotspotStatusText = view.findViewById(R.id.hotspot_status_text);
        networkInfoText = view.findViewById(R.id.network_info_text);
        wifiButton = view.findViewById(R.id.wifi_button);
        hotspotButton = view.findViewById(R.id.hotspot_button);
        
        // RTSP推流控制
        rtspStatusText = view.findViewById(R.id.rtsp_status_text);
        rtspUrlText = view.findViewById(R.id.rtsp_url_text);
        networkInterfaceSpinner = view.findViewById(R.id.network_interface_spinner);
        streamTypeRadioGroup = view.findViewById(R.id.stream_type_radio_group);
        cameraStreamRadio = view.findViewById(R.id.radio_camera_stream);
        screenStreamRadio = view.findViewById(R.id.radio_screen_stream);
        startRtspButton = view.findViewById(R.id.start_rtsp_button);
        stopRtspButton = view.findViewById(R.id.stop_rtsp_button);
    }
    
    /**
     * 初始化网络管理器
     */
    private void initNetworkManager() {
        if (getContext() != null && wifiPanelLauncher != null) {
            tpNetworkMonitor = new TpNetworkMonitor(getContext(), this, wifiPanelLauncher);
        } else {
            Log.w(TAG, "无法初始化NetworkManager：Context或wifiPanelLauncher为null");
        }
    }
    
    /**
     * 设置事件监听器
     */
    private void setupEventListeners() {
        // WiFi按钮点击事件
        wifiButton.setOnClickListener(v -> {
            if (tpNetworkMonitor != null) {
                tpNetworkMonitor.toggleWifi(isWifiEnabled);
                if (isWifiEnabled) {
                    networkInfoText.setText("正在断开WiFi连接...");
                } else {
                    networkInfoText.setText("请在系统设置中选择并连接WiFi");
                }
            }
        });
        
        // 热点按钮点击事件
        hotspotButton.setOnClickListener(v -> {
            if (tpNetworkMonitor != null) {
                tpNetworkMonitor.openHotspotSettings();
                networkInfoText.setText("请在系统设置中配置热点");
            }
        });
        
        // 网络接口选择事件
        networkInterfaceSpinner.setOnItemSelectedListener(new AdapterView.OnItemSelectedListener() {
            @Override
            public void onItemSelected(AdapterView<?> parent, View view, int position, long id) {
                if (position >= 0 && position < networkInterfaces.size()) {
                    selectedNetworkInterface = networkInterfaces.get(position).getName();
                    updateStartButtonState();
                }
            }
            
            @Override
            public void onNothingSelected(AdapterView<?> parent) {
                selectedNetworkInterface = null;
                updateStartButtonState();
            }
        });
        
        // 流类型选择事件
        streamTypeRadioGroup.setOnCheckedChangeListener((group, checkedId) -> {
            updateStartButtonState();
        });
        
        // RTSP推流控制按钮事件
        startRtspButton.setOnClickListener(v -> startRtspStreaming());
        stopRtspButton.setOnClickListener(v -> stopRtspStreaming());
    }
    
    /**
     * 初始化RTSP控制
     */
    private void initRtspControls() {
        // 检查videoSystem是否可用
        if (videoSystem == null) {
            disableRtspControls("视频系统不可用");
            return;
        }

        // 设置RTSP状态
        updateRtspStatus();

        // 加载网络接口列表
        loadNetworkInterfaces();
    }
    
    /**
     * 启动RTSP推流
     */
    private void startRtspStreaming() {
        if (videoSystem == null || selectedNetworkInterface == null) {
            Toast.makeText(getContext(), "视频系统或网络接口不可用", Toast.LENGTH_SHORT).show();
            return;
        }

        networkInfoText.setText("正在启动RTSP推流，请稍候...");
        startRtspButton.setEnabled(false);

        // 获取选择的流类型
        TpVideoSystem.StreamType streamType = cameraStreamRadio.isChecked() ?
                TpVideoSystem.StreamType.CAMERA : TpVideoSystem.StreamType.SCREEN;

        // 在后台线程启动推流
        new Thread(() -> {
            final boolean success = videoSystem.startStreaming(streamType, selectedNetworkInterface);

            // 在UI线程更新界面
            if (getActivity() != null) {
                getActivity().runOnUiThread(() -> {
                    if (success) {
                        isRtspStreaming = true;
                        startRtspButton.setEnabled(false);
                        stopRtspButton.setEnabled(true);
                        networkInterfaceSpinner.setEnabled(false);
                        streamTypeRadioGroup.setEnabled(false);

                        String streamTypeName = streamType == TpVideoSystem.StreamType.CAMERA ? "摄像头流" : "屏幕流";
                        networkInfoText.setText("已成功启动" + streamTypeName + "推流，网络接口: " + selectedNetworkInterface);

                        updateRtspStatus();
                    } else {
                        startRtspButton.setEnabled(true);
                        Toast.makeText(getContext(), "启动RTSP推流失败", Toast.LENGTH_SHORT).show();
                        networkInfoText.setText("启动RTSP推流失败，请检查系统日志");
                    }
                });
            }
        }).start();
    }
    
    /**
     * 停止RTSP推流
     */
    private void stopRtspStreaming() {
        if (videoSystem == null) {
            return;
        }

        networkInfoText.setText("正在停止RTSP推流，请稍候...");
        stopRtspButton.setEnabled(false);

        // 在后台线程停止推流
        new Thread(() -> {
            final boolean success = videoSystem.stopStreaming();

            // 在UI线程更新界面
            if (getActivity() != null) {
                getActivity().runOnUiThread(() -> {
                    if (success) {
                        isRtspStreaming = false;
                        startRtspButton.setEnabled(true);
                        stopRtspButton.setEnabled(false);
                        networkInterfaceSpinner.setEnabled(true);
                        streamTypeRadioGroup.setEnabled(true);

                        networkInfoText.setText("已停止RTSP推流");
                        updateRtspStatus();
                    } else {
                        stopRtspButton.setEnabled(true);
                        Toast.makeText(getContext(), "停止RTSP推流失败", Toast.LENGTH_SHORT).show();
                        networkInfoText.setText("停止RTSP推流失败，请检查系统日志");
                    }
                });
            }
        }).start();
    }
    
    /**
     * 加载网络接口列表
     */
    private void loadNetworkInterfaces() {
        if (tpNetworkMonitor == null) {
            return;
        }
        
        networkInterfaces = tpNetworkMonitor.getAvailableNetworkInterfaces();
        
        if (networkInterfaces.isEmpty()) {
            networkInfoText.setText("未检测到可用的网络接口");
            disableRtspControls("未检测到可用的网络接口");
            return;
        }
        
        // 准备显示数据
        List<String> displayList = new ArrayList<>();
        for (TpNetworkMonitor.NetworkInterfaceInfo info : networkInterfaces) {
            displayList.add(info.getDescription() + " (" + info.getName() + "): " + info.getIpAddress());
        }
        
        // 设置下拉列表适配器
        if (getContext() != null) {
            ArrayAdapter<String> adapter = new ArrayAdapter<>(
                    getContext(),
                    android.R.layout.simple_spinner_item,
                    displayList);
            adapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item);
            networkInterfaceSpinner.setAdapter(adapter);
            
            // 默认选择第一个
            if (!networkInterfaces.isEmpty()) {
                selectedNetworkInterface = networkInterfaces.get(0).getName();
                networkInterfaceSpinner.setSelection(0);
            }
        }
        
        updateStartButtonState();
    }
    
    /**
     * 更新启动按钮状态
     */
    private void updateStartButtonState() {
        if (videoSystem == null || selectedNetworkInterface == null || isRtspStreaming) {
            startRtspButton.setEnabled(false);
        } else {
            startRtspButton.setEnabled(true);
        }
    }
    
    /**
     * 更新RTSP状态显示
     */
    private void updateRtspStatus() {
        if (videoSystem == null) {
            rtspStatusText.setText("RTSP状态：服务不可用");
            rtspUrlText.setText("RTSP URL：");
            return;
        }

        boolean isStreaming = videoSystem.isStreaming();
        isRtspStreaming = isStreaming;

        if (isStreaming) {
            TpVideoSystem.StreamType streamType = videoSystem.getCurrentStreamType();
            String streamTypeName = streamType == TpVideoSystem.StreamType.CAMERA ? "摄像头流" : "屏幕流";
            rtspStatusText.setText("RTSP状态：推流中 (" + streamTypeName + ")");

            String url = videoSystem.getStreamUrl();
            rtspUrlText.setText("RTSP URL：" + (url != null ? url : "未知"));

            startRtspButton.setEnabled(false);
            stopRtspButton.setEnabled(true);
            networkInterfaceSpinner.setEnabled(false);
            streamTypeRadioGroup.setEnabled(false);
        } else {
            rtspStatusText.setText("RTSP状态：未启动");
            rtspUrlText.setText("RTSP URL：");

            startRtspButton.setEnabled(selectedNetworkInterface != null);
            stopRtspButton.setEnabled(false);
            networkInterfaceSpinner.setEnabled(true);
            streamTypeRadioGroup.setEnabled(true);
        }
    }
    
    /**
     * 禁用RTSP控制
     */
    private void disableRtspControls(String reason) {
        rtspStatusText.setText("RTSP状态：" + reason);
        rtspUrlText.setText("RTSP URL：");
        startRtspButton.setEnabled(false);
        stopRtspButton.setEnabled(false);
        networkInterfaceSpinner.setEnabled(false);
        streamTypeRadioGroup.setEnabled(false);
    }
    
    /**
     * 更新依赖对象（供外部调用）
     */
    public void updateDependencies(TpVideoSystem videoSystem) {
        this.videoSystem = videoSystem;

        // 重新初始化RTSP控制
        if (getView() != null) {
            initRtspControls();
        }
    }
    
    @Override
    public void onStart() {
        super.onStart();
        // 开始网络监控
        if (tpNetworkMonitor != null) {
            tpNetworkMonitor.startMonitoring();
        }
        
        // 刷新RTSP状态
        updateRtspStatus();
        
        // 重新加载网络接口
        loadNetworkInterfaces();
    }
    
    @Override
    public void onStop() {
        super.onStop();
        // 停止网络监控
        if (tpNetworkMonitor != null) {
            tpNetworkMonitor.stopMonitoring();
        }
    }
    
    // TpNetworkMonitor.NetworkStateListener 接口实现
    @Override
    public void onWifiStateChanged(boolean isConnected, String ssid) {
        isWifiEnabled = isConnected;
        if (isConnected) {
            wifiStatusText.setText("WIFI状态：已连接");
            wifiButton.setText("断开WIFI");
            networkInfoText.setText("已连接到: " + ssid);
            loadNetworkInterfaces();
        } else {
            wifiStatusText.setText("WIFI状态：未连接");
            wifiButton.setText("连接WIFI");
            
            String currentInfo = networkInfoText.getText().toString();
            if (!currentInfo.contains("热点") && !currentInfo.contains("请在系统设置")) {
                networkInfoText.setText("");
            }
            loadNetworkInterfaces();
        }
    }
    
    @Override
    public void onEthernetStateChanged(boolean isConnected) {
        if (isConnected) {
            ethernetStatusText.setText("以太网状态：已连接");
            loadNetworkInterfaces();
        } else {
            ethernetStatusText.setText("以太网状态：未连接");
            loadNetworkInterfaces();
        }
    }
    
    @Override
    public void onHotspotStateChanged(boolean isEnabled, String hotspotInfo) {
        if (isEnabled) {
            hotspotStatusText.setText("热点状态：已开启");
            hotspotButton.setText("热点设置");
            
            if (!hotspotInfo.isEmpty()) {
                networkInfoText.setText(hotspotInfo);
            }
            loadNetworkInterfaces();
        } else {
            hotspotStatusText.setText("热点状态：未开启");
            hotspotButton.setText("开启热点");
            
            if (!isWifiEnabled && networkInfoText.getText().toString().contains("热点")) {
                networkInfoText.setText("");
            }
            loadNetworkInterfaces();
        }
    }
}
