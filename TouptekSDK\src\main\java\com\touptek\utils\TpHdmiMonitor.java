package com.touptek.utils;

import android.util.Log;
import android.util.Size;

import java.io.BufferedReader;
import java.io.File;
import java.io.FileReader;
import java.io.IOException;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * TpHdmiMonitor 类用于检测和管理HDMI输入状态。
 * <p>
 * 此类提供了HDMI状态的监控、信号锁定检测和分辨率/帧率获取功能。
 * 通过读取系统文件来检测HDMI的插拔状态和信号锁定状态，并通过回调接口通知状态变化。
 * 采用单例模式确保全局唯一实例。
 * </p>
 * 
 * <p><b>主要功能：</b></p>
 * <ul>
 *   <li>监控HDMI连接状态变化</li>
 *   <li>检测HDMI信号锁定状态</li>
 *   <li>获取HDMI输入分辨率</li>
 *   <li>获取HDMI输入帧率</li>
 *   <li>提供状态变化回调机制</li>
 * </ul>
 * 
 * <p><b>使用示例：</b></p>
 * <pre>{@code
 * // 获取HdmiService实例
 * TpHdmiMonitor hdmiService = TpHdmiMonitor.getInstance();
 * 
 * // 设置状态监听器
 * hdmiService.setHdmiListener(new TpHdmiMonitor.HdmiListener() {
 *     @Override
 *     public void onHdmiStatusChanged(boolean isConnected) {
 *         if (isConnected) {
 *             Log.d(TAG, "HDMI已连接并锁定信号");
 *             
 *             // 获取分辨率和帧率
 *             Size resolution = TpHdmiMonitor.getHdmiResolution();
 *             int frameRate = TpHdmiMonitor.getFrameRate();
 *             
 *             if (resolution != null) {
 *                 Log.d(TAG, "HDMI分辨率: " + resolution.getWidth() + "x" + 
 *                       resolution.getHeight() + " @" + frameRate + "fps");
 *             }
 *         } else {
 *             Log.d(TAG, "HDMI已断开或信号丢失");
 *         }
 *     }
 * });
 * 
 * // 初始化并开始监控
 * hdmiService.init();
 * 
 * // 停止监控
 * hdmiService.stop();
 * }</pre>
 * 
 */
public class TpHdmiMonitor
{
    /* 日志标签 */
    private static final String TAG = "TpHdmiMonitor";

    /* HDMI IN 状态文件路径（需注意权限配置） */
    private final String mHdmiRxDevicePath = "/sys/kernel/debug/hdmirx/status";

    /* HDMI 状态文件对象 */
    private static File mHdmiRxFile;

    /* 保存上一次状态，防止重复触发 */
    private String lastStatus = "";
    
    /* 保存上一次锁定状态 */
    private boolean lastSignalLocked = false;

    /* 控制线程终止 */
    private boolean threadStatus = false;

    /* HDMI 状态变化监听器 */
    private HdmiListener listener;

    /* 标志位，用于处理首次检测 */
    private boolean firstFlag = true;


    /* 单例实例 */
    private static TpHdmiMonitor instance = new TpHdmiMonitor();

    /**
     * 定义 HDMI 状态变化监听接口。
     * <p>
     * 当 HDMI 状态发生变化时，会触发此接口的回调方法。
     * </p>
     */
    public interface HdmiListener
    {
        /**
         * 当 HDMI 状态发生变化时调用。
         *
         * @param isConnected 当前 HDMI 状态
         */
        void onHdmiStatusChanged(boolean isConnected);
    }

    /**
     * 设置 HDMI 状态变化监听器。
     * <p>
     * 此方法会设置一个监听器，当 HDMI 状态发生变化时会触发回调。
     * </p>
     *
     * @param listener 监听器实例
     */
    public void setHdmiListener(HdmiListener listener)
    {
        this.listener = listener;

        /* 打印日志 */
        Log.d(TAG, "setHdmiListener");
    }

    /**
     * 获取 TpHdmiMonitor 的单例实例。
     * <p>
     * 此方法返回 TpHdmiMonitor 的唯一实例。
     * </p>
     *
     * @return TpHdmiMonitor 实例
     */
    public static TpHdmiMonitor getInstance()
    {
        /* 打印日志 */
        Log.d(TAG, "getInstance");
        return instance;
    }

    /**
     * 初始化 HDMI 状态检测。
     * <p>
     * 此方法会启动一个线程，定期读取 HDMI 状态文件以检测状态变化。
     * </p>
     */
    public void init()
    {
        Log.d(TAG, "init: 开始读取 HDMI 状态");
        mHdmiRxFile = new File(mHdmiRxDevicePath);
        threadStatus = false;
        new ReadThread().start();

        /* 打印日志 */
        Log.d(TAG, "init");
    }

    /**
     * 停止 HDMI 状态检测线程。
     * <p>
     * 此方法会终止 HDMI 状态检测线程，并释放相关资源。
     * </p>
     */
    public void stop()
    {
        threadStatus = true;

        /* 打印日志 */
        Log.d(TAG, "stop");
    }

    /**
     * 解析 HDMI 状态信息，检查信号是否锁定。
     *
     * @param statusContent HDMI 状态文件内容
     * @return 信号是否锁定
     */
    private boolean isSignalLocked(String statusContent) {
        if (statusContent == null) {
            return false;
        }

        // 检查是否包含"Clk-Ch:Lock"表示时钟通道锁定
        return statusContent.contains("Clk-Ch:Lock");
    }

    /**
     * 用于读取 HDMI 状态的线程类。
     * <p>
     * 此线程会定期读取 HDMI 状态文件，并在状态发生变化时触发回调。
     * </p>
     */
    private class ReadThread extends Thread
    {
        /* 连续检测计数器 */
        private int stableConnectionCounter = 0;
        
        /* 需要连续稳定的次数 */
        private static final int REQUIRED_STABLE_COUNT = 8;
        
        /* 当前通知的连接状态 */
        private boolean currentNotifiedState = false;
        
        @Override
        public void run()
        {
            while (!threadStatus)
            {
                try
                {
                    /* 每 300 毫秒检测一次 */
                    Thread.sleep(300);
    
                    /* 读取 HDMI 状态文件 */
                    FileReader reader = new FileReader(mHdmiRxFile);
                    BufferedReader bufReader = new BufferedReader(reader);
                    StringBuilder statusBuilder = new StringBuilder();
                    String line;
    
                    // 读取完整的状态内容
                    while ((line = bufReader.readLine()) != null) {
                        statusBuilder.append(line).append("\n");
                    }
                    String currentStatus = statusBuilder.toString();
                    bufReader.close();
    
                    // 检查连接状态和信号锁定状态
                    boolean isPhysicallyConnected = currentStatus.contains("plugin");
                    boolean isSignalLocked = isSignalLocked(currentStatus);
                    boolean isReallyConnected = isPhysicallyConnected && isSignalLocked;
    
                    // 保存当前状态以检测变化
                    boolean statusChanged = !currentStatus.equals(lastStatus);
                    lastStatus = currentStatus;
                    lastSignalLocked = isSignalLocked;
                    
                    // HDMI断开处理 - 立即触发回调
                    if (!isPhysicallyConnected && currentNotifiedState) {
                        stableConnectionCounter = 0;
                        currentNotifiedState = false;

                        // 触发断开回调
                        if (listener != null) {
                            listener.onHdmiStatusChanged(false);
                            Log.d(TAG, "HDMI状态更新: 物理连接=false, 信号锁定=false, 实际连接状态=false");
                        }
                        
                        continue;  // 断开后直接进入下一个循环
                    }
                    /* HDMI连接处理 - 需要连续稳定 */
                    if (isReallyConnected)
                    {
                        // 连接并锁定，增加计数
                        stableConnectionCounter++;

                        if(stableConnectionCounter < REQUIRED_STABLE_COUNT)
                        {
                            Log.d(TAG, "CameraHDMI连接稳定计数: " + stableConnectionCounter + "/" + REQUIRED_STABLE_COUNT);
                        }

                        // 达到稳定要求
                        if (stableConnectionCounter >= REQUIRED_STABLE_COUNT && !currentNotifiedState) {
                            currentNotifiedState = true;

                            // 触发连接回调
                            if (listener != null) {
                                listener.onHdmiStatusChanged(true);
                                Log.d(TAG, "HDMI信号已稳定锁定，触发连接回调");
                                Log.d(TAG, "HDMI状态更新: 物理连接=true, 信号锁定=true, 实际连接状态=true");
                            }
                        }
                    }
                    else if (isPhysicallyConnected && !isSignalLocked)
                    {
                        // 已连接但未锁定
                        
                        // 如果之前是已连接状态，需要通知断开
                        if (currentNotifiedState) {
                            currentNotifiedState = false;
                            if (listener != null) {
                                listener.onHdmiStatusChanged(false);
                                Log.d(TAG, "HDMI信号丢失，触发断开回调");
                            }
                        }

                        // 重置计数
                        if (stableConnectionCounter > 0) {
                            Log.d(TAG, "HDMI已连接但信号未锁定，重置稳定计数");
                            stableConnectionCounter = 0;
                        }

                    }
                }
                catch (IOException e)
                {
                    Log.d(TAG, "读取 HDMI 状态异常：" + e.toString());
                }
                catch (InterruptedException e)
                {
                    Log.e(TAG, "线程中断", e);
                }
            }
    
            /* 打印日志 */
            Log.d(TAG, "ReadThread run");
        }
    }

    /**
     * 获取当前HDMI输入的分辨率
     *
     * @return 分辨率Size对象，包含宽度和高度信息，如果无法获取则返回null
     */
    public static Size getHdmiResolution() {
        if (mHdmiRxFile == null || !mHdmiRxFile.exists())
        {
            Log.e(TAG, "HDMI状态文件不存在");
            return null;
        }

        try {
            String statusContent = readHdmiStatusFile();
            if (statusContent == null)
            {
                return null;
            }

            // 使用正则表达式匹配分辨率信息
            // 格式如: Timing: 3840x2160p60 (4400x2250)
            Pattern pattern = Pattern.compile("Timing:\\s*(\\d+)x(\\d+)p");
            Matcher matcher = pattern.matcher(statusContent);

            if (matcher.find())
            {
                int width = Integer.parseInt(matcher.group(1));
                int height = Integer.parseInt(matcher.group(2));
                Log.d(TAG, "解析到HDMI分辨率: " + width + "x" + height);
                return new Size(width, height);
            }
            else
            {
                Log.w(TAG, "无法从HDMI状态中解析分辨率: " + statusContent);
                return null;
            }
        }
        catch (Exception e)
        {
            Log.e(TAG, "获取HDMI分辨率时发生异常", e);
            return null;
        }
    }

    /**
     * 获取当前HDMI输入的帧率
     *
     * @return 当前帧率值，单位为fps(帧/秒)，如果无法获取则返回0
     */
    public static int getFrameRate() {
        if (mHdmiRxFile == null || !mHdmiRxFile.exists())
        {
            Log.e(TAG, "HDMI状态文件不存在");
            return 0;
        }

        try
        {
            String statusContent = readHdmiStatusFile();
            if (statusContent == null)
            {
                return 0;
            }

            // 使用正则表达式匹配帧率信息
            // 格式如: Timing: 3840x2160p60 (4400x2250)
            Pattern pattern = Pattern.compile("(\\d+)x(\\d+)p(\\d+)");
            Matcher matcher = pattern.matcher(statusContent);

            if (matcher.find())
            {
                int frameRate = Integer.parseInt(matcher.group(3));
                Log.d(TAG, "解析到HDMI帧率: " + frameRate);
                return frameRate;
            } else
            {
                Log.w(TAG, "无法从HDMI状态中解析帧率: " + statusContent);
                return 0;
            }
        }
        catch (Exception e)
        {
            Log.e(TAG, "获取HDMI帧率时发生异常", e);
            return 0;
        }
    }

    /**
     * 读取HDMI状态文件内容
     *
     * @return 状态文件内容，如果读取失败则返回null
     */
    private static String readHdmiStatusFile()
    {
        try
        {
            FileReader reader = new FileReader(mHdmiRxFile);
            BufferedReader bufReader = new BufferedReader(reader);
            StringBuilder statusBuilder = new StringBuilder();
            String line;

            while ((line = bufReader.readLine()) != null)
            {
                statusBuilder.append(line).append("\n");
            }
            bufReader.close();

            return statusBuilder.toString();
        }
        catch (IOException e)
        {
            Log.e(TAG, "读取HDMI状态文件失败", e);
            return null;
        }
    }
}
