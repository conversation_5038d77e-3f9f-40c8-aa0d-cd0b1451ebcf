package com.touptek.video.internal.rtsp.service;

import android.content.Context;
import android.media.MediaCodec;
import android.media.MediaFormat;
import android.os.Handler;
import android.os.Looper;
import android.util.Log;

import com.pedro.rtspserver.RtspServer;
import com.touptek.video.internal.TpVideoEncoder;
import com.touptek.video.internal.rtsp.TpRtspManager;
import com.touptek.video.internal.rtsp.config.RTSPConfig;

import java.nio.ByteBuffer;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * RTSP推流器
 * 
 * 此类仅供RTSPManager内部使用，不应直接被外部访问。
 */
public class RTSPStreamer implements TpVideoEncoder.VideoDataOutputCallback {
    private static final String TAG = "RTSPStreamer";
    private static final int MAX_RETRIES = 5;
    private static final long RETRY_DELAY_MS = 1000;
    private static final long FORMAT_TIMEOUT_MS = 5000;
    
    private final Context context;
    private final RTSPConfig config;
    private TpRtspManager.StreamType streamType; // 改为RTSPManager.StreamType
    private ProjectionData projectionData;
    private TpVideoEncoder tpVideoEncoder;
    private final TpRtspManager.StreamStateListener listener; // 改为RTSPManager.StreamStateListener
    private final Handler mainHandler = new Handler(Looper.getMainLooper());
    
    // 连接服务相关
    private RTSPServiceConnection serviceConnection;
    
    // 相机推流相关
    private RtspServer rtspServer;
    private final AtomicBoolean isClientConnected = new AtomicBoolean(false);
    private ByteBuffer spsBuffer;
    private ByteBuffer ppsBuffer;
    private boolean formatConfigured = false;
    private int retryCount = 0;
    private ScheduledExecutorService scheduler;
    private long startTimeMs = 0;
    private Runnable clientMonitorTask;
    
    // 状态相关
    private boolean isStreaming = false;
    private String rtspUrl;
    
    /**
     * 创建RTSP推流器
     * @param context 上下文
     * @param config 推流配置
     * @param streamType 推流类型
     * @param projectionData 投影数据（屏幕推流时需要）
     * @param tpVideoEncoder 视频编码器（相机推流时需要）
     * @param listener 状态监听器
     */
    public RTSPStreamer(
            Context context,
            RTSPConfig config,
            TpRtspManager.StreamType streamType, // 修改参数类型
            ProjectionData projectionData,
            TpVideoEncoder tpVideoEncoder,
            TpRtspManager.StreamStateListener listener) { // 修改参数类型
        
        this.context = context;
        this.config = config;
        this.streamType = streamType;
        this.projectionData = projectionData;
        this.tpVideoEncoder = tpVideoEncoder;
        this.listener = listener;
    }
    
    /**
     * 开始推流
     * @return 是否成功启动推流
     */
    public boolean startStream() {
        if (isStreaming) {
            Log.w(TAG, "Stream is already running");
            return false;
        }
        
        // 根据类型执行不同的推流启动逻辑
        if (streamType == TpRtspManager.StreamType.SCREEN) { // 使用RTSPManager.StreamType
            return startScreenStream();
        } else {
            return startCameraStream();
        }
    }
    
    /**
     * 直接启动摄像头推流（供RTSPManager调用）
     * 
     * @param tpVideoEncoder 视频编码器
     * @param config 配置信息
     * @param listener 状态监听器
     * @return 是否成功启动推流
     */
    public boolean startCameraStreaming(TpVideoEncoder tpVideoEncoder, RTSPConfig config, TpRtspManager.StreamStateListener listener) {
        this.tpVideoEncoder = tpVideoEncoder;
        this.streamType = TpRtspManager.StreamType.CAMERA;
        
        // 使用传入的配置更新当前配置（如果需要）
        updateConfig(config);
        
        return startCameraStream();
    }
    
    /**
     * 直接启动屏幕推流（供RTSPManager调用）
     * 
     * @param projectionData 投影数据
     * @param config 配置信息
     * @param listener 状态监听器
     * @return 是否成功启动推流
     */
    public boolean startScreenStreaming(ProjectionData projectionData, RTSPConfig config, TpRtspManager.StreamStateListener listener) {
        this.projectionData = projectionData;
        this.streamType = TpRtspManager.StreamType.SCREEN;
        
        // 使用传入的配置更新当前配置（如果需要）
        updateConfig(config);
        
        return startScreenStream();
    }
    
    /**
     * 更新配置信息
     * 
     * @param newConfig 新的配置信息
     */
    private void updateConfig(RTSPConfig newConfig) {
        if (newConfig != null) {
            // 因为我们的RTSPConfig是不可变的（除了host），所以只能更新一部分属性
            if (newConfig.getHost() != null && !newConfig.getHost().isEmpty()) {
                this.config.setHost(newConfig.getHost());
            }
        }
    }
    
    /**
     * 停止推流
     */
    public void stopStream() {
        if (!isStreaming) {
            return;
        }
        
        // 根据类型执行不同的推流停止逻辑
        if (streamType == TpRtspManager.StreamType.SCREEN) { // 使用RTSPManager.StreamType
            stopScreenStream();
        } else {
            stopCameraStream();
        }
    }
    
    /**
     * 获取RTSP URL
     * @return RTSP URL
     */
    public String getRtspUrl() {
        return rtspUrl;
    }
    
    /**
     * 是否正在推流
     * @return 是否正在推流
     */
    public boolean isStreaming() {
        return isStreaming;
    }
    
    /**
     * 获取推流类型
     * @return 推流类型
     */
    public TpRtspManager.StreamType getStreamType() { // 修改返回类型
        return streamType;
    }
    
    /**
     * 切换到屏幕推流模式
     * 如果当前正在推流，会先停止当前推流
     * 
     * @param projectionData 投影数据
     * @return 是否成功切换
     */
    public boolean switchToScreenStream(ProjectionData projectionData) {
        if (isStreaming) {
            stopStream();
        }
        
        this.streamType = TpRtspManager.StreamType.SCREEN; // 使用RTSPManager.StreamType
        this.projectionData = projectionData;
        this.tpVideoEncoder = null;
        
        return startStream();
    }
    
    /**
     * 切换到相机推流模式
     * 如果当前正在推流，会先停止当前推流
     * 
     * @param tpVideoEncoder 视频编码器
     * @return 是否成功切换
     */
    public boolean switchToCameraStream(TpVideoEncoder tpVideoEncoder) {
        if (isStreaming) {
            stopStream();
        }
        
        this.streamType = TpRtspManager.StreamType.CAMERA; // 使用RTSPManager.StreamType
        this.projectionData = null;
        this.tpVideoEncoder = tpVideoEncoder;
        
        // 重置摄像头流相关的状态信息
        resetCameraStreamState();
        
        return startStream();
    }
    
    /**
     * 重置摄像头流状态信息
     * 清除之前的SPS/PPS配置，确保新的VideoEncoder能够正确配置
     */
    private void resetCameraStreamState() {
        formatConfigured = false;
        spsBuffer = null;
        ppsBuffer = null;
        retryCount = 0;
        isClientConnected.set(false);
        
        // 停止之前的调度器任务
        if (scheduler != null && !scheduler.isShutdown()) {
            scheduler.shutdownNow();
            scheduler = null;
        }
        
        // 重置RTSP服务器
        if (rtspServer != null) {
            try {
                rtspServer.stopServer();
            } catch (Exception e) {
                Log.e(TAG, "Error stopping previous RTSP server", e);
            }
            rtspServer = null;
        }
        
        Log.d(TAG, "Camera stream state reset for new encoder");
    }
    
    /**
     * 开始屏幕推流
     */
    private boolean startScreenStream() {
        if (projectionData == null) {
            notifyStreamError("未获取屏幕捕获权限");
            return false;
        }
        
        if (!projectionData.isValid()) {
            notifyStreamError("用户拒绝了屏幕捕获权限");
            return false;
        }
        
        try {
            // 创建RTSPServiceConnection
            serviceConnection = new RTSPServiceConnection(
                context,
                config,
                projectionData,
                url -> notifyStreamStarted(url),
                this::notifyStreamStopped
            );
            
            // 绑定服务
            return serviceConnection.bind();
        } catch (Exception e) {
            Log.e(TAG, "Failed to start screen stream", e);
            notifyStreamError("启动屏幕推流失败: " + e.getMessage());
            return false;
        }
    }
    
    /**
     * 停止屏幕推流
     */
    private void stopScreenStream() {
        try {
            if (serviceConnection != null) {
                serviceConnection.unbind();
                serviceConnection = null;
            }
            notifyStreamStopped();
        } catch (Exception e) {
            Log.e(TAG, "Failed to stop screen stream", e);
            notifyStreamError("停止屏幕推流失败: " + e.getMessage());
        }
    }
    
    /**
     * 开始相机推流
     */
    private boolean startCameraStream() {
        if (tpVideoEncoder == null) {
            notifyStreamError("未提供视频编码器");
            return false;
        }
        
        try {
            // 记录开始时间
            startTimeMs = System.currentTimeMillis();
            retryCount = 0;
            
            // 初始化调度器
            if (scheduler != null && !scheduler.isShutdown()) {
                scheduler.shutdownNow();
            }
            scheduler = Executors.newScheduledThreadPool(2);
            
            // 初始化RTSP服务器
            rtspServer = new RtspServer(new RtspConnectChecker(), config.getPort());
            rtspServer.setLogs(false);
            rtspServer.startServer();
            
            // 生成RTSP URL
            rtspUrl = "rtsp://" + rtspServer.getServerIp() + ":" + rtspServer.getPort();
            
            // 设置视频编码器回调
            tpVideoEncoder.setOutputCallback(this);
            
            // 立即请求一个关键帧
            requestKeyFrame();
            
            // 尝试从编码器直接获取当前格式
            tryGetFormatFromEncoder();
            
            isStreaming = true;
            
            // 设置超时检查
            scheduler.schedule(this::checkFormatTimeout, FORMAT_TIMEOUT_MS, TimeUnit.MILLISECONDS);
            
            // 设置客户端监控任务
            clientMonitorTask = this::monitorClientState;
            // 每5秒检查一次客户端状态
            scheduler.scheduleAtFixedRate(clientMonitorTask, 5, 5, TimeUnit.SECONDS);
            
            // 通知流已开始
            notifyStreamStarted(rtspUrl);
            
            Log.d(TAG, "Camera RTSP stream started: " + rtspUrl);
            return true;
        } catch (Exception e) {
            Log.e(TAG, "Failed to start camera RTSP stream", e);
            stopCameraStream();
            notifyStreamError("启动摄像头推流失败: " + e.getMessage());
            return false;
        }
    }
    
    /**
     * 停止相机推流
     */
    private void stopCameraStream() {
        try {
            // 停止调度器
            if (scheduler != null && !scheduler.isShutdown()) {
                scheduler.shutdownNow();
                scheduler = null;
            }
            
            // 移除视频编码器回调
            if (tpVideoEncoder != null) {
                tpVideoEncoder.setOutputCallback(null);
            }
            
            // 停止RTSP服务器
            if (rtspServer != null) {
                rtspServer.stopServer();
                rtspServer = null;
            }
            
            formatConfigured = false;
            retryCount = 0;
            isStreaming = false;
            
            // 通知流已停止
            notifyStreamStopped();
            
            Log.d(TAG, "Camera RTSP stream stopped");
        } catch (Exception e) {
            Log.e(TAG, "Error stopping camera RTSP stream", e);
            notifyStreamError("停止摄像头推流失败: " + e.getMessage());
        }
    }
    
    //-------------------- 相机推流相关辅助方法 --------------------
    
    /**
     * 监控客户端状态
     */
    private void monitorClientState() {
        if (!isStreaming) return;
        
        try {
            // 检查服务器是否需要重启
            if (rtspServer != null && isClientConnected.get()) {
                Log.d(TAG, "Client disconnected, but stream should continue. Server is ready for reconnection.");
                isClientConnected.set(false);
                
                // 可以在这里添加UI通知，但不停止流
                Log.i(TAG, "客户端已断开连接，服务器等待重连");
            }
            
            // 如果有需要，可以在这里添加服务器重启逻辑
            if (rtspServer == null && isStreaming) {
                Log.d(TAG, "RTSP server needs to be restarted");
                rtspServer = new RtspServer(new RtspConnectChecker(), config.getPort());
                rtspServer.startServer();
                
                // 如果已经配置了格式，重新设置视频信息
                if (formatConfigured && spsBuffer != null && ppsBuffer != null) {
                    rtspServer.setVideoInfo(spsBuffer.duplicate(), ppsBuffer.duplicate(), null);
                }
            }
        } catch (Exception e) {
            Log.e(TAG, "Error in client monitor task", e);
        }
    }
    
    /**
     * 尝试从编码器直接获取当前格式
     */
    private void tryGetFormatFromEncoder() {
        try {
            MediaFormat currentFormat = tpVideoEncoder.getEncoderOutputFormat();
            if (currentFormat != null) {
                Log.d(TAG, "Got encoder format directly: " + currentFormat);
                
                ByteBuffer sps = currentFormat.getByteBuffer("csd-0");
                ByteBuffer pps = currentFormat.getByteBuffer("csd-1");
                
                if (sps != null && pps != null) {
                    Log.d(TAG, "Found SPS/PPS directly from encoder");
                    onVideoFormatChanged(currentFormat, sps, pps);
                } else {
                    Log.d(TAG, "Format available but SPS/PPS not found, will wait for key frame");
                }
            } else {
                Log.d(TAG, "Encoder format not available yet, will wait for key frame");
            }
        } catch (Exception e) {
            Log.e(TAG, "Error getting encoder format", e);
        }
    }
    
    /**
     * 请求关键帧
     */
    private void requestKeyFrame() {
        try {
            if (tpVideoEncoder != null) {
                tpVideoEncoder.requestKeyFrame();
                Log.d(TAG, "Requested key frame from encoder");
            }
        } catch (Exception e) {
            Log.e(TAG, "Failed to request key frame", e);
        }
    }
    
    /**
     * 检查格式是否超时，如果超时则重试
     */
    private void checkFormatTimeout() {
        if (!isStreaming) return;
        
        if (!formatConfigured) {
            long elapsedMs = System.currentTimeMillis() - startTimeMs;
            Log.w(TAG, "Format not received after " + elapsedMs + "ms, retrying...");
            
            if (retryCount < MAX_RETRIES) {
                retryCount++;
                Log.d(TAG, "Retry #" + retryCount + " for key frame");
                
                // 再次请求关键帧
                requestKeyFrame();
                
                // 再次尝试获取格式
                tryGetFormatFromEncoder();
                
                // 安排下一次超时检查
                scheduler.schedule(this::checkFormatTimeout, RETRY_DELAY_MS, TimeUnit.MILLISECONDS);
            } else {
                Log.e(TAG, "Failed to get video format after " + MAX_RETRIES + " retries");
                notifyStreamError("无法获取视频格式信息，请检查摄像头设置");
                stopStream();
            }
        }
    }
    
    //-------------------- VideoDataOutputCallback 实现 --------------------
    
    /**
     * 当视频格式改变时接收SPS和PPS
     */
    @Override
    public void onVideoFormatChanged(MediaFormat format, ByteBuffer spsBuffer, ByteBuffer ppsBuffer) {
        if (!isStreaming || rtspServer == null) return;
        
        try {
            Log.d(TAG, "Video format changed: " + format);
            
            if (spsBuffer == null || ppsBuffer == null) {
                Log.e(TAG, "SPS or PPS is null, cannot configure RTSP");
                return;
            }
            
            // 检查SPS和PPS是否有效
            if (spsBuffer.remaining() == 0 || ppsBuffer.remaining() == 0) {
                Log.e(TAG, "SPS or PPS has no data (zero length)");
                return;
            }
            
            // 保存SPS和PPS的副本，以便重连时使用
            this.spsBuffer = spsBuffer.duplicate();
            this.ppsBuffer = ppsBuffer.duplicate();
            
            Log.d(TAG, "SPS size: " + this.spsBuffer.remaining() + ", PPS size: " + this.ppsBuffer.remaining());
            
            // 设置视频参数到RTSP服务器
            rtspServer.setVideoInfo(this.spsBuffer.duplicate(), this.ppsBuffer.duplicate(), null);
            formatConfigured = true;
            
            Log.d(TAG, "SPS/PPS configured for RTSP successfully");
        } catch (Exception e) {
            Log.e(TAG, "Error handling video format change", e);
        }
    }
    
    /**
     * 接收编码后的H.264视频数据
     */
    @Override
    public void onVideoDataAvailable(ByteBuffer encodedData, MediaCodec.BufferInfo bufferInfo) {
        if (!isStreaming || rtspServer == null) return;
        
        try {
            // 检查是否为关键帧
            boolean isKeyFrame = (bufferInfo.flags & MediaCodec.BUFFER_FLAG_KEY_FRAME) != 0;
            if (isKeyFrame) {
//                Log.d(TAG, "Key frame received, size: " + bufferInfo.size);
                
                // 如果还没配置格式，但收到了关键帧，尝试再次获取格式
                if (!formatConfigured) {
                    Log.d(TAG, "Received key frame but format not configured yet, trying to get format");
                    tryGetFormatFromEncoder();
                }
            }
            
            // 确保格式已配置
            if (!formatConfigured) {
                // 如果还没配置，丢弃这一帧
                Log.v(TAG, "Format not configured yet, discarding frame");
                return;
            }
            
            // 如果SPS和PPS存在但尚未配置，现在配置它们
            if (spsBuffer != null && ppsBuffer != null && !formatConfigured) {
                rtspServer.setVideoInfo(spsBuffer.duplicate(), ppsBuffer.duplicate(), null);
                formatConfigured = true;
                Log.d(TAG, "Late SPS/PPS configuration");
            }
            
            try {
                // 发送视频数据到RTSP服务器
                ByteBuffer buffer = encodedData.duplicate();
                rtspServer.sendVideo(buffer, bufferInfo);
            } catch (Exception e) {
                // 捕获发送错误，但不停止推流
                Log.e(TAG, "Error sending video data (client may be disconnected): " + e.getMessage());
            }
        } catch (Exception e) {
            Log.e(TAG, "Error processing video data", e);
        }
    }
    
    //-------------------- 通知相关方法 --------------------
    
    /**
     * 通知流开始
     */
    private void notifyStreamStarted(String url) {
        isStreaming = true;
        rtspUrl = url;
        if (listener != null) {
            mainHandler.post(() -> listener.onStreamStarted(url));
        }
    }
    
    /**
     * 通知流停止
     */
    private void notifyStreamStopped() {
        isStreaming = false;
        if (listener != null) {
            mainHandler.post(() -> listener.onStreamStopped());
        }
    }
    
    /**
     * 通知流错误
     */
    private void notifyStreamError(String errorMessage) {
        if (listener != null) {
            mainHandler.post(() -> listener.onStreamError(errorMessage));
        }
    }
    
    /**
     * RTSP连接检查器
     */
    private class RtspConnectChecker implements com.pedro.common.ConnectChecker {
        @Override
        public void onConnectionStarted(String url) {
            Log.d(TAG, "RTSP connection started: " + url);
        }
        
        @Override
        public void onConnectionSuccess() {
            Log.d(TAG, "RTSP connection success");
            isClientConnected.set(true);
            
            // 客户端连接成功时请求一个关键帧，以便快速显示画面
            requestKeyFrame();
        }
        
        @Override
        public void onConnectionFailed(String reason) {
            // 只记录错误，不中断推流
            Log.e(TAG, "RTSP connection failed: " + reason);
            isClientConnected.set(false);
        }
        
        @Override
        public void onNewBitrate(long bitrate) {
//            Log.d(TAG, "RTSP new bitrate: " + bitrate);
        }
        
        @Override
        public void onDisconnect() {
            Log.d(TAG, "RTSP client disconnected");
            isClientConnected.set(false);
        }
        
        @Override
        public void onAuthError() {
            Log.e(TAG, "RTSP authentication error");
        }
        
        @Override
        public void onAuthSuccess() {
            Log.d(TAG, "RTSP authentication success");
        }
    }
}
