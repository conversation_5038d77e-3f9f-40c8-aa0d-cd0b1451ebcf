package com.touptek.video.internal;

import android.media.MediaCodec;
import android.media.MediaCodecInfo;
import android.media.MediaFormat;
import android.media.MediaMuxer;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.util.Log;
import android.util.Size;
import android.view.Surface;

import com.touptek.utils.TpFileManager;
import com.touptek.video.TpVideoConfig;

import java.io.File;
import java.nio.ByteBuffer;
import java.util.function.BiConsumer;
import java.util.function.Consumer;

/**
 * TpVideoEncoder 类负责视频编码、存储监控以及文件大小限制的处理。
 * <p>
 * 此类提供了初始化编码器、开始录制、停止录制以及释放资源的方法。
 * 它还支持存储空间监控和 FAT32 文件系统的文件大小限制。
 * </p>
 * 
 * <p><b>主要功能：</b></p>
 * <ul>
 *   <li>高性能视频编码 (H.264/AVC)</li>
 *   <li>支持多种分辨率 (最高支持4K)</li>
 *   <li>存储空间自动监控</li>
 *   <li>FAT32文件系统大小限制处理</li>
 *   <li>编码参数动态调整</li>
 *   <li>丰富的回调接口</li>
 * </ul>
 * 
 * <p><b>使用示例：</b></p>
 * <pre>
 * // 创建编码器（推荐使用TpVideoConfig）
 * TpVideoEncoder encoder = TpVideoEncoder.builder()
 *     .setPreviewSurface(surfaceTexture)
 *     .setTpVideoConfig(TpVideoConfig.create4K())
 *     .onSurfaceAvailable(surface -> {
 *         // 在此处理编码器Surface可用事件
 *     })
 *     .onStorageFull(() -> {
 *         // 存储空间不足时的处理
 *     })
 *     .onError((errorType, e) -> {
 *         // 错误处理
 *     })
 *     .build();
 * 
 * // 开始录制
 * encoder.startRecording(outputPath);
 * 
 * // 停止录制
 * encoder.stopRecording();
 * 
 * // 释放资源
 * encoder.release();
 * </pre>
 * 
 */
public class TpVideoEncoder
{
    /* 日志标签 */
    private static final String TAG = "TpVideoEncoder";

    /* 编码器实例 */
    private MediaCodec encoder;

    /* 解码器实例 */
    private MediaCodec decoder;

    /* 媒体复用器，用于将编码后的数据写入文件 */
    private MediaMuxer mediaMuxer;

    /* 视频轨道索引 */
    private int videoTrackIndex = -1;

    /* 是否已启动复用器 */
    private boolean isMuxerStarted = false;

    /* 是否正在录制 */
    private boolean isRecording = false;

    /* 编码器输入的 Surface */
    private Surface encoderInputSurface;

    /* 解码器输出的 Surface */
    private Surface decoderOutputSurface;

    /* 输出文件路径 */
    private String outputPath;

    /* FAT32 文件系统的最大文件大小限制 (3.9GB) */
    private static final long MAX_FILE_SIZE_FAT32 = (long) (3.9 * 1024 * 1024 * 1024);

    /* 最小存储空间限制 (50MB) */
    private static final long MIN_STORAGE_SPACE_BYTES = 50 * 1024 * 1024;

    /* 存储空间检查的处理器 */
    private Handler storageCheckHandler;

    /* 存储空间检查的任务 */
    private Runnable storageCheckRunnable;

    /* 是否为 FAT32 文件系统 */
    private boolean isFat32 = false;

    /* 视频数据输出回调 */
    private VideoDataOutputCallback mOutputCallback;

    /* 视频尺寸 */
    private Size videoSize;
    
    /* 预览Surface */
    private Surface previewSurface;
    
    /* Surface可用通知回调 */
    private Consumer<Surface> onSurfaceAvailableHandler;
    
    /* 存储满通知回调 */
    private Runnable onStorageFullHandler;
    
    /* 错误通知回调 */
    private BiConsumer<String, Exception> onErrorHandler;
    
    /* 文件大小限制达到通知回调 */
    private Runnable onFileSizeLimitReachedHandler;
    
    /* 保存完成通知回调 */
    private Consumer<String> onSaveCompleteHandler;
    
    /* TpVideoConfig配置 */
    private TpVideoConfig tpVideoConfig;

    /**
     * 动态设置视频编码比特率
     * <p>
     * 此方法允许在编码过程中动态调整视频比特率，可用于根据网络状况
     * 或存储需求实时调整视频质量。设置后会在下一个关键帧生效。
     * </p>
     *
     * @param bitrate 新的比特率，单位为bps(比特/秒)，推荐值：1080p为5,000,000，4K为10,000,000
     * @return 是否成功设置比特率
     */
    public boolean setBitrate(int bitrate) {
        if (encoder == null) {
            Log.e(TAG, "Cannot set bitrate: encoder is null");
            return false;
        }
        
        try {
            Bundle params = new Bundle();
            params.putInt(MediaCodec.PARAMETER_KEY_VIDEO_BITRATE, bitrate);
            encoder.setParameters(params);
            
            // 可选：请求一个关键帧使变更立即生效
            requestKeyFrame();
            
            Log.d(TAG, "Video bitrate changed to: " + bitrate + " bps");
            return true;
        } catch (Exception e) {
            Log.e(TAG, "Failed to set bitrate: " + e.getMessage());
            return false;
        }
    }

    /**
     * 视频数据输出回调接口
     * <p>该接口用于接收编码后的视频数据和格式信息，可用于RTSP推流或其他实时处理</p>
     */
    public interface VideoDataOutputCallback 
    {
        /**
         * 当视频格式发生变化时调用
         * 
         * @param format 新的视频格式
         * @param spsBuffer SPS参数集缓冲区
         * @param ppsBuffer PPS参数集缓冲区
         */
        void onVideoFormatChanged(MediaFormat format, ByteBuffer spsBuffer, ByteBuffer ppsBuffer);
        
        /**
         * 当有新的视频数据可用时调用
         * 
         * @param encodedData 编码后的视频数据
         * @param bufferInfo 缓冲区信息，包含时间戳、标志等
         */
        void onVideoDataAvailable(ByteBuffer encodedData, MediaCodec.BufferInfo bufferInfo);
    }

    /**
     * 设置视频数据输出回调
     * <p>设置此回调后，编码的视频数据将通过回调方法提供，而不是写入文件</p>
     * 
     * @param callback 回调接口实现
     */
    public void setOutputCallback(VideoDataOutputCallback callback) 
    {
        this.mOutputCallback = callback;
    }

    /**
     * 私有构造函数，通过Builder模式创建实例
     * <p>请使用{@link #builder()}方法创建Builder实例</p>
     */
    private TpVideoEncoder()
    {
        /* 私有化构造函数 */
    }

    /**
     * 创建Builder实例
     * <p>VideoEncoder使用Builder模式创建实例，以便灵活配置各种参数和回调</p>
     * 
     * @return 新的Builder实例
     */
    public static Builder builder() 
    {
        return new Builder();
    }

    /**
     * 通知Surface可用
     * 
     * @param surface 可用的Surface
     */
    private void notifySurfaceAvailable(Surface surface) 
    {
        if (onSurfaceAvailableHandler != null) 
        {
            onSurfaceAvailableHandler.accept(surface);
        }
    }

    /**
     * 通知存储空间已满
     */
    private void notifyStorageFull() 
    {
        if (onStorageFullHandler != null) 
        {
            onStorageFullHandler.run();
        }
    }

    /**
     * 通知发生错误
     * 
     * @param errorType 错误类型
     * @param e 异常对象
     */
    private void notifyError(String errorType, Exception e) 
    {
        if (onErrorHandler != null) 
        {
            onErrorHandler.accept(errorType, e);
        }
    }

    /**
     * 通知文件大小限制已达到
     */
    private void notifyFileSizeLimitReached() 
    {
        if (onFileSizeLimitReachedHandler != null) 
        {
            onFileSizeLimitReachedHandler.run();
        }
    }

    /**
     * 通知保存完成
     * 
     * @param filePath 已保存文件的路径
     */
    private void notifySaveComplete(String filePath) 
    {
        Log.d(TAG, "notifySaveComplete called with path: " + filePath);
        if (onSaveCompleteHandler != null) 
        {
            // 确保回调在主线程中执行，因为可能会操作UI
            new Handler(Looper.getMainLooper()).post(() -> {
                Log.d(TAG, "Executing onSaveCompleteHandler on main thread");
                onSaveCompleteHandler.accept(filePath);
            });
        }
        else
        {
            Log.e(TAG, "onSaveCompleteHandler is null");
        }
    }

    /**
     * VideoEncoder的构建器类
     * <p>
     * 使用构建器模式创建和配置VideoEncoder实例，支持链式调用设置各种参数和回调。
     * 通过此构建器可以灵活地配置编码器的尺寸、预览Surface、回调处理器等。
     * </p>
     * 
     * <p><b>使用示例：</b></p>
     * <pre>{@code
     * TpVideoEncoder encoder = TpVideoEncoder.builder()
     *     .setSize(new Size(1920, 1080))
     *     .setTpVideoConfig(TpVideoConfig.create1080p())
     *     .onSurfaceAvailable(surface -> {
     *         // 处理编码器Surface可用事件
     *     })
     *     .onError((errorType, e) -> {
     *         // 处理错误
     *     })
     *     .build();
     * }</pre>
     */
    public static class Builder 
    {
        /* VideoEncoder实例 */
        private final TpVideoEncoder tpVideoEncoder;
        
        /* 编码器配置 - 支持TpVideoConfig */
        private TpVideoConfig tpVideoConfig;

        /**
         * 私有构造函数，通过{@link TpVideoEncoder#builder()}方法创建实例
         */
        private Builder() 
        {
            tpVideoEncoder = new TpVideoEncoder();
        }
        
        /**
         * 设置视频尺寸
         * <p>
         * 配置编码视频的宽度和高度。常用尺寸包括：
         * <ul>
         *   <li>4K: 3840×2160</li>
         *   <li>1080p: 1920×1080</li>
         *   <li>720p: 1280×720</li>
         * </ul>
         *
         * @param videoSize 视频尺寸（宽×高）
         * @return Builder实例，用于链式调用
         */
        public Builder setSize(Size videoSize) 
        {
            tpVideoEncoder.videoSize = videoSize;
            return this;
        }
        
        /**
         * 设置预览Surface
         * <p>
         * 配置用于显示预览的Surface。这通常是一个SurfaceView或TextureView的Surface。
         * </p>
         *
         * @param previewSurface 预览Surface
         * @return Builder实例，用于链式调用
         */
        public Builder setPreviewSurface(Surface previewSurface) 
        {
            tpVideoEncoder.previewSurface = previewSurface;
            return this;
        }
        
        /**
         * 设置Surface可用回调
         * <p>
         * 当编码器的输入Surface准备就绪时，此回调会被触发。
         * 应用可以将此Surface传递给相机或其他视频源。
         * </p>
         *
         * @param handler Surface可用时的回调处理器
         * @return Builder实例，用于链式调用
         */
        public Builder onSurfaceAvailable(Consumer<Surface> handler) 
        {
            tpVideoEncoder.onSurfaceAvailableHandler = handler;
            return this;
        }
        
        /**
         * 设置存储空间不足回调
         * <p>
         * 当存储空间不足以继续录制时，此回调会被触发。
         * 应用应在此回调中停止录制并通知用户。
         * </p>
         *
         * @param handler 存储空间不足时的回调处理器
         * @return Builder实例，用于链式调用
         */
        public Builder onStorageFull(Runnable handler) 
        {
            tpVideoEncoder.onStorageFullHandler = handler;
            return this;
        }
        
        /**
         * 设置错误回调
         * <p>
         * 当编码过程中发生错误时，此回调会被触发。
         * 第一个参数是错误类型，第二个参数是异常对象（如果有）。
         * </p>
         *
         * @param handler 错误发生时的回调处理器
         * @return Builder实例，用于链式调用
         */
        public Builder onError(BiConsumer<String, Exception> handler) 
        {
            tpVideoEncoder.onErrorHandler = handler;
            return this;
        }
        
        /**
         * 设置文件大小限制达到回调
         * <p>
         * 当录制文件达到系统限制大小（通常是FAT32的4GB限制）时，此回调会被触发。
         * 应用可以在此回调中创建新文件继续录制。
         * </p>
         *
         * @param handler 文件大小限制达到时的回调处理器
         * @return Builder实例，用于链式调用
         */
        public Builder onFileSizeLimitReached(Runnable handler) 
        {
            tpVideoEncoder.onFileSizeLimitReachedHandler = handler;
            return this;
        }
        
        /**
         * 设置保存完成回调
         * <p>
         * 当视频文件保存完成时，此回调会被触发。
         * 参数为保存的文件路径。
         * </p>
         *
         * @param handler 保存完成时的回调处理器
         * @return Builder实例，用于链式调用
         */
        public Builder onSaveComplete(Consumer<String> handler) 
        {
            tpVideoEncoder.onSaveCompleteHandler = handler;
            return this;
        }
        
        /**
         * 设置TpVideoConfig配置
         * <p>
         * 配置编码器的详细参数，如比特率、帧率、编码格式等。
         * 使用TpVideoConfig统一配置系统。
         * </p>
         *
         * @param config TpVideoConfig配置对象
         * @return Builder实例，用于链式调用
         */
        public Builder setTpVideoConfig(TpVideoConfig config)
        {
            this.tpVideoConfig = config;
            tpVideoEncoder.tpVideoConfig = config;
            return this;
        }



        /**
         * 构建VideoEncoder实例
         * <p>根据之前设置的所有参数创建并初始化VideoEncoder</p>
         *
         * @return 配置好的VideoEncoder实例
         * @throws IllegalStateException 如果必要参数未设置
         */
        public TpVideoEncoder build()
        {
            // 检查配置参数
            if (tpVideoEncoder.videoSize == null && tpVideoEncoder.tpVideoConfig == null)
            {
                throw new IllegalStateException("必须设置视频分辨率或TpVideoConfig");
            }
            if (tpVideoEncoder.previewSurface == null)
            {
                throw new IllegalStateException("必须设置预览Surface");
            }

            // 使用TpVideoConfig设置视频尺寸
            if (tpVideoEncoder.tpVideoConfig != null)
            {
                tpVideoEncoder.videoSize = new Size(tpVideoEncoder.tpVideoConfig.getWidth(),
                                                 tpVideoEncoder.tpVideoConfig.getHeight());
            }

            tpVideoEncoder.initialize(tpVideoEncoder.videoSize, tpVideoEncoder.previewSurface);
            return tpVideoEncoder;
        }
    }

    /**
     * 初始化编码器和解码器。
     * <p>
     * 此方法会根据指定的视频尺寸和比特率配置编码器和解码器，并启动它们。
     * </p>
     *
     * @param videoSize
     *        视频尺寸（宽度和高度）。
     * @param decoderSurface
     *        解码器输出的 Surface，用于显示解码后的视频。
     */
    public void initialize(Size videoSize, Surface decoderSurface) 
    {
        this.videoSize = videoSize;
        this.previewSurface = decoderSurface;
        try 
        {
            Log.d(TAG, "Initializing TpVideoEncoder");

            // 确定MIME类型和创建MediaFormat
            String mimeType = "video/avc"; // 默认H.264
            MediaFormat format;

            if (tpVideoConfig != null) {
                // 使用TpVideoConfig创建MediaFormat
                mimeType = mapVideoCodecToMimeType(tpVideoConfig.getCodec());
                format = createMediaFormatFromTpVideoConfig(tpVideoConfig, videoSize);
            }
            else
            {
                // 使用默认配置
                format = MediaFormat.createVideoFormat("video/avc", videoSize.getWidth(), videoSize.getHeight());
                format.setInteger(MediaFormat.KEY_BIT_RATE, 30_000_000);
                format.setInteger(MediaFormat.KEY_FRAME_RATE, 60);
                format.setInteger(MediaFormat.KEY_COLOR_FORMAT, MediaCodecInfo.CodecCapabilities.COLOR_FormatSurface);
                format.setInteger(MediaFormat.KEY_I_FRAME_INTERVAL, 1);
                format.setInteger(MediaFormat.KEY_BITRATE_MODE, MediaCodecInfo.EncoderCapabilities.BITRATE_MODE_VBR);
                format.setInteger(MediaFormat.KEY_COLOR_RANGE, MediaFormat.COLOR_RANGE_FULL);
                format.setInteger(MediaFormat.KEY_COLOR_STANDARD, MediaFormat.COLOR_STANDARD_BT709);
                format.setInteger(MediaFormat.KEY_PROFILE, MediaCodecInfo.CodecProfileLevel.AVCProfileHigh);
            }

            encoder = MediaCodec.createEncoderByType(mimeType);

            encoder.configure(format, null, null, MediaCodec.CONFIGURE_FLAG_ENCODE);

            /* 创建编码器输入的 Surface */
            encoderInputSurface = encoder.createInputSurface();

            /* 初始化解码器 */
            decoder = MediaCodec.createDecoderByType("video/avc");
            decoderOutputSurface = decoderSurface; /* 设置解码器输出的 Surface */
            decoder.configure(format, decoderOutputSurface, null, 0);

            /* 启动编码器和解码器 */
            encoder.start();
            decoder.start();

            /* 通知回调接口 Surface 可用 */
            notifySurfaceAvailable(encoderInputSurface);

            /* 启动编码和解码循环 */
            startEncodingDecoding();
        } 
        catch (Exception e) 
        {
            Log.e(TAG, "Error initializing TpVideoEncoder", e);
        }
    }

    /**
     * 获取编码器当前输出格式
     * 
     * @return 编码器输出格式
     */
    public MediaFormat getEncoderOutputFormat() 
    {
        if (encoder != null) 
        {
            try 
            {
                /* 尝试获取实际的当前格式 */
                MediaFormat format = encoder.getOutputFormat();
                Log.d(TAG, "Getting current encoder format: " + format);
                return format;
            } 
            catch (IllegalStateException e) 
            {
                Log.e(TAG, "Failed to get encoder format", e);
                return null;
            }
        }
        return null;
    }

    /**
     * 请求生成关键帧
     * <p>
     * 强制编码器生成一个IDR关键帧。这在动态调整编码参数后或需要
     * 重新同步视频流时非常有用。
     * </p>
     *
     */
    public void requestKeyFrame() 
    {
        if (encoder != null) 
        {
            Bundle params = new Bundle();
            params.putInt(MediaCodec.PARAMETER_KEY_REQUEST_SYNC_FRAME, 0);
            encoder.setParameters(params);
        }
    }

    /**
     * 开始录制视频。
     * <p>
     * 此方法会初始化 MediaMuxer 并启动存储监控。
     * </p>
     *
     * @param outputPath
     *        输出文件路径，用于保存录制的视频。
     */
    public void startRecording(String outputPath) 
    {
        Log.d(TAG, "Start recording with output path: " + outputPath);
        this.outputPath = outputPath; /* 动态设置输出路径 */
        isRecording = true;

        /* 检测文件系统类型 */
        String fileSystemType = TpFileManager.getFileSystemType(outputPath);
        isFat32 = "vfat".equalsIgnoreCase(fileSystemType);
        Log.d(TAG, "File system type: " + fileSystemType);

        /* 初始化 MediaMuxer */
        try 
        {
            mediaMuxer = new MediaMuxer(outputPath, MediaMuxer.OutputFormat.MUXER_OUTPUT_MPEG_4);
            MediaFormat outputFormat = encoder.getOutputFormat();
            videoTrackIndex = mediaMuxer.addTrack(outputFormat);
            mediaMuxer.start();
            isMuxerStarted = true;
            Log.d(TAG, "MediaMuxer started successfully, isMuxerStarted=" + isMuxerStarted);
        } 
        catch (Exception e) 
        {
            Log.e(TAG, "Error starting MediaMuxer", e);
            notifyError("MediaMuxer initialization failed", e);
            return;
        }

        /* 启动存储监控 */
        startStorageMonitoring();
    }

    /**
     * 启动存储监控。
     * <p>
     * 此方法会定期检查存储空间是否不足或文件大小是否超过限制。
     * 如果检测到问题，会通过回调接口通知调用者。
     * </p>
     */
    private void startStorageMonitoring() 
    {
        storageCheckHandler = new Handler(Looper.getMainLooper());
        storageCheckRunnable = new Runnable() 
        {
            @Override
            public void run() 
            {
                long availableSpace = TpFileManager.getAvailableStorageSpace(outputPath);
                if (availableSpace < MIN_STORAGE_SPACE_BYTES) 
                {
                    handleStorageError("Insufficient storage space");
                } 
                else if (isFat32 && getCurrentFileSize() > MAX_FILE_SIZE_FAT32) 
                {
                    handleFileSizeLimit();
                } 
                else 
                {
                    storageCheckHandler.postDelayed(this, 1000); /* 每 1 秒检查一次 */
                }
            }

            /**
             * 处理存储错误情况
             * 
             * @param errorType 错误类型描述
             */
            private void handleStorageError(String errorType) 
            {
                Log.e(TAG, errorType + ", stopping recording");
                stopRecording();
                notifyStorageFull();
            }

            /**
             * 处理文件大小超限情况
             */
            private void handleFileSizeLimit() 
            {
                Log.e(TAG, "FAT32 file size limit reached, stopping recording");
                stopRecording();
                notifyFileSizeLimitReached();
            }
        };
        storageCheckHandler.post(storageCheckRunnable);
    }

    /**
     * 获取当前文件的大小。
     * <p>
     * 此方法会检查输出文件的大小（以字节为单位）。
     * </p>
     *
     * @return
     *        返回当前文件的大小（字节）。如果文件不存在，则返回 0。
     */
    private long getCurrentFileSize() 
    {
        File file = new File(outputPath);
        return file.exists() ? file.length() : 0;
    }

    /**
     * 停止录制视频。
     * <p>
     * 此方法会停止存储监控并释放 MediaMuxer 资源。
     * </p>
     */
    public void stopRecording()
    {
        Log.d(TAG, "Stop recording, isMuxerStarted=" + isMuxerStarted + ", isRecording=" + isRecording);

        // 防止重复调用
        if (!isRecording) {
            Log.w(TAG, "stopRecording called but recording is not active");
            return;
        }

        isRecording = false;

        /* 停止存储监控 */
        if (storageCheckHandler != null)
        {
            storageCheckHandler.removeCallbacks(storageCheckRunnable);
        }

        // 使用synchronized确保线程安全
        synchronized (this) {
            if (isMuxerStarted && mediaMuxer != null)
            {
                final String finalOutputPath = outputPath; /* 保存当前输出路径的副本 */
                isMuxerStarted = false; // 立即设置为false，防止重复停止

                new Thread(() ->
                {
                    try
                    {
                        /* 停止 MediaMuxer */
                        Log.d(TAG, "Stopping MediaMuxer...");
                        mediaMuxer.stop();
                        /* 释放 MediaMuxer 资源 */
                        mediaMuxer.release();
                        Log.d(TAG, "MediaMuxer stopped and released");

                        /* 通知MP4保存完成 */
                        if (finalOutputPath != null && !finalOutputPath.isEmpty()) {
                            Log.d(TAG, "About to notify save complete with path: " + finalOutputPath);
                            notifySaveComplete(finalOutputPath);
                        } else {
                            Log.e(TAG, "Invalid output path for save complete notification");
                        }
                    }
                    catch (Exception e)
                    {
                        Log.e(TAG, "Error stopping MediaMuxer", e);
                        // 确保错误回调在主线程中执行
                        if (onErrorHandler != null) {
                            // 使用Handler确保在主线程中执行
                            new Handler(Looper.getMainLooper()).post(() -> {
                                onErrorHandler.accept("MediaMuxer stop error", e);
                            });
                        }
                    }
                    finally
                    {
                        synchronized (TpVideoEncoder.this) {
                            mediaMuxer = null; /* 避免内存泄漏 */
                        }
                    }
                }).start();
            } else {
                Log.w(TAG, "stopRecording called but isMuxerStarted is false or mediaMuxer is null");
            }
        }
    }

    /**
     * 释放编码器和解码器的资源。
     * <p>
     * 此方法会停止并释放编码器、解码器和 MediaMuxer 的资源。
     * </p>
     */
    public void release() 
    {
        if (encoder != null) 
        {
            encoder.stop();
            encoder.release();
            encoder = null;
        }
        if (decoder != null) 
        {
            decoder.stop();
            decoder.release();
            decoder = null;
        }
        if (mediaMuxer != null)
        {
            try
            {
                // 只有在Muxer已启动的情况下才调用stop()
                if (isMuxerStarted) {
                    Log.d(TAG, "Stopping MediaMuxer in release()");
                    mediaMuxer.stop();
                }
                mediaMuxer.release();
                Log.d(TAG, "MediaMuxer released in release()");
            }
            catch (Exception e)
            {
                Log.e(TAG, "Error releasing MediaMuxer", e);
                // 在release方法中不调用notifyError，避免在应用关闭时显示Toast
                // notifyError("Failed to release resources", e);
            }
            mediaMuxer = null;
        }
        isMuxerStarted = false;
    }

    /**
     * 启动编码和解码循环。
     * <p>
     * 此方法会在单独的线程中运行，处理编码器和解码器的输入和输出缓冲区。
     * </p>
     */
    private void startEncodingDecoding() 
    {
        new Thread(() -> 
        {
            android.os.Process.setThreadPriority(android.os.Process.THREAD_PRIORITY_DISPLAY);
            try 
            {
                Log.d(TAG, "Starting encoding/decoding loop");
                MediaCodec.BufferInfo bufferInfo = new MediaCodec.BufferInfo();
                while (true) 
                {
                    /* 获取编码器输出 */
                    int outputBufferIndex = encoder.dequeueOutputBuffer(bufferInfo, 10000);

                    if (outputBufferIndex == MediaCodec.INFO_OUTPUT_FORMAT_CHANGED) 
                    {
                        MediaFormat newFormat = encoder.getOutputFormat();

                        if (mOutputCallback != null) 
                        {
                            ByteBuffer spsBuffer = newFormat.getByteBuffer("csd-0");
                            ByteBuffer ppsBuffer = newFormat.getByteBuffer("csd-1");
                            mOutputCallback.onVideoFormatChanged(newFormat, spsBuffer, ppsBuffer);
                        }
                    } 
                    else if (outputBufferIndex >= 0) 
                    {
                        ByteBuffer encodedData = encoder.getOutputBuffer(outputBufferIndex);
                        if (encodedData != null && bufferInfo.size > 0) 
                        {
                            /* 保存当前位置和限制，以便稍后恢复 */
                            int initialPosition = encodedData.position();
                            int initialLimit = encodedData.limit();

                            /* 写入 MP4 文件（仅在录制时） */
                            if (isRecording && isMuxerStarted) 
                            {
                                encodedData.position(bufferInfo.offset);
                                encodedData.limit(bufferInfo.offset + bufferInfo.size);
                                mediaMuxer.writeSampleData(videoTrackIndex, encodedData, bufferInfo);
                            }

                            if (mOutputCallback != null) 
                            {
                                /* 准备要发送的数据 */
                                encodedData.position(bufferInfo.offset);
                                encodedData.limit(bufferInfo.offset + bufferInfo.size);

                                /* 复制一个缓冲区信息对象 */
                                MediaCodec.BufferInfo info = new MediaCodec.BufferInfo();
                                info.set(0, bufferInfo.size, bufferInfo.presentationTimeUs, bufferInfo.flags);

                                mOutputCallback.onVideoDataAvailable(encodedData, info);
                            }

                            /* 恢复位置，用于解码 */
                            encodedData.position(initialPosition);
                            encodedData.limit(initialLimit);

                            /* 将编码数据传递给解码器 */
                            int inputBufferIndex = decoder.dequeueInputBuffer(10000);
                            if (inputBufferIndex >= 0) 
                            {
                                ByteBuffer inputBuffer = decoder.getInputBuffer(inputBufferIndex);
                                if (inputBuffer != null) 
                                {
                                    inputBuffer.clear();
                                    inputBuffer.put(encodedData);
                                    decoder.queueInputBuffer(inputBufferIndex, 0, bufferInfo.size, bufferInfo.presentationTimeUs, bufferInfo.flags);
                                }
                            }
                        }
                        encoder.releaseOutputBuffer(outputBufferIndex, false);
                    }

                    /* 获取解码器输出 */
                    int decodedOutputIndex = decoder.dequeueOutputBuffer(bufferInfo, 10000);
                    if (decodedOutputIndex >= 0) 
                    {
                        decoder.releaseOutputBuffer(decodedOutputIndex, true); /* 渲染到解码器的输出 Surface */
                    }
                }
            } 
            catch (Exception e) 
            {
                Log.e(TAG, "Error in encoding/decoding loop", e);
            }
        }).start();
    }

    // ===== TpVideoConfig支持方法 =====

    /**
     * 从TpVideoConfig创建MediaFormat
     *
     * @param config TpVideoConfig配置
     * @param videoSize 视频尺寸
     * @return 配置好的MediaFormat
     */
    private MediaFormat createMediaFormatFromTpVideoConfig(TpVideoConfig config, Size videoSize) {
        Log.d(TAG, "从TpVideoConfig创建MediaFormat: " + config.toString());

        String mimeType = mapVideoCodecToMimeType(config.getCodec());
        MediaFormat format = MediaFormat.createVideoFormat(mimeType, videoSize.getWidth(), videoSize.getHeight());

        // 基础参数
        format.setInteger(MediaFormat.KEY_BIT_RATE, config.getBitRate());
        format.setInteger(MediaFormat.KEY_FRAME_RATE, config.getFrameRate());
        format.setInteger(MediaFormat.KEY_I_FRAME_INTERVAL, config.getKeyFrameInterval());

        // 颜色格式
        format.setInteger(MediaFormat.KEY_COLOR_FORMAT, MediaCodecInfo.CodecCapabilities.COLOR_FormatSurface);

        // 比特率模式
        format.setInteger(MediaFormat.KEY_BITRATE_MODE, mapBitrateModeToEncoder(config.getBitrateMode()));

        // 标准视频参数
        format.setInteger(MediaFormat.KEY_COLOR_RANGE, MediaFormat.COLOR_RANGE_FULL);
        format.setInteger(MediaFormat.KEY_COLOR_STANDARD, MediaFormat.COLOR_STANDARD_BT709);

        // H.264特定参数（硬编码优化配置）
        if (config.getCodec() == TpVideoConfig.VideoCodec.H264) {
            format.setInteger(MediaFormat.KEY_PROFILE, MediaCodecInfo.CodecProfileLevel.AVCProfileHigh);
        }

        // 低延迟性能优化参数（硬编码）
        try {
            format.setInteger(MediaFormat.KEY_PRIORITY, 0);          // 实时优先级
            if (config.getBitrateMode() == TpVideoConfig.BitrateMode.CQ) {
                format.setInteger(MediaFormat.KEY_QUALITY, 75);      // CQ模式质量
            }
            Log.d(TAG, "应用低延迟优化配置");
        } catch (Exception e) {
            Log.w(TAG, "部分性能优化参数不支持: " + e.getMessage());
        }

        Log.d(TAG, "MediaFormat创建完成: " + format.toString());
        return format;
    }

    /**
     * 映射TpVideoConfig.VideoCodec到MIME类型
     */
    private String mapVideoCodecToMimeType(TpVideoConfig.VideoCodec codec) {
        switch (codec) {
            case H264:
                return "video/avc";
            case H265:
                return "video/hevc";
            default:
                Log.w(TAG, "未知的视频编码格式: " + codec + ", 使用默认H264");
                return "video/avc";
        }
    }

    /**
     * 映射TpVideoConfig.BitrateMode到编码器比特率模式
     */
    private int mapBitrateModeToEncoder(TpVideoConfig.BitrateMode mode) {
        switch (mode) {
            case CBR:
                return MediaCodecInfo.EncoderCapabilities.BITRATE_MODE_CBR;
            case VBR:
                return MediaCodecInfo.EncoderCapabilities.BITRATE_MODE_VBR;
            case CQ:
                return MediaCodecInfo.EncoderCapabilities.BITRATE_MODE_CQ;
            default:
                Log.w(TAG, "未知的比特率模式: " + mode + ", 使用默认VBR");
                return MediaCodecInfo.EncoderCapabilities.BITRATE_MODE_VBR;
        }
    }

}
