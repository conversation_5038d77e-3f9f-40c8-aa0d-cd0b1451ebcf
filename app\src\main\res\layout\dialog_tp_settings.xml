<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:background="@drawable/dialog_background">

    <!-- 标题栏 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:background="#2196F3"
        android:padding="16dp"
        android:gravity="center_vertical">

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="ToupTek 设置"
            android:textColor="#FFFFFF"
            android:textSize="18sp"
            android:textStyle="bold" />

        <ImageButton
            android:id="@+id/btn_close_settings"
            android:layout_width="32dp"
            android:layout_height="32dp"
            android:background="?android:attr/selectableItemBackgroundBorderless"
            android:src="@android:drawable/ic_menu_close_clear_cancel"
            android:tint="#FFFFFF"
            android:contentDescription="关闭设置" />

    </LinearLayout>

    <!-- 主内容区域：两面板布局 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:orientation="horizontal">

        <!-- 左侧导航菜单面板 -->
        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:orientation="vertical"
            android:background="#FFFFFF">

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="设置分类"
                android:textSize="16sp"
                android:textStyle="bold"
                android:textColor="#333333"
                android:padding="16dp"
                android:background="#F8F8F8" />

            <View
                android:layout_width="match_parent"
                android:layout_height="1dp"
                android:background="#E0E0E0" />

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/settings_menu_recycler"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:scrollbars="vertical" />

        </LinearLayout>

        <!-- 分隔线 -->
        <View
            android:layout_width="1dp"
            android:layout_height="match_parent"
            android:background="#E0E0E0" />

        <!-- 右侧内容面板 -->
        <FrameLayout
            android:id="@+id/settings_content_frame"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="2"
            android:background="#FFFFFF">

            <!-- 默认提示内容 -->
            <LinearLayout
                android:id="@+id/default_content"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:orientation="vertical"
                android:gravity="center"
                android:padding="32dp">

                <ImageView
                    android:layout_width="64dp"
                    android:layout_height="64dp"
                    android:src="@android:drawable/ic_menu_manage"
                    android:tint="#CCCCCC"
                    android:layout_marginBottom="16dp" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="请选择左侧的设置分类"
                    android:textSize="16sp"
                    android:textColor="#999999"
                    android:gravity="center" />

            </LinearLayout>

        </FrameLayout>

    </LinearLayout>

</LinearLayout>
