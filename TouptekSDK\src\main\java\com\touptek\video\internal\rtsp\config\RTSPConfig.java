package com.touptek.video.internal.rtsp.config;

/**
 * RTSP推流配置类
 * 包含所有推流相关的配置参数
 * 
 * <AUTHOR>
 * @version 1.0
 */
public class RTSPConfig 
{
    /**
     * 视频宽度
     */
    private final int width;
    
    /**
     * 视频高度
     */
    private final int height;
    
    /**
     * 视频帧率
     */
    private final int frameRate;
    
    /**
     * 视频比特率，单位bps
     */
    private final int videoBitrate;
    
    /**
     * RTSP服务器端口
     */
    private final int port;
    
    /**
     * 是否录制麦克风音频
     */
    private final boolean recordMic;
    
    /**
     * 关键帧间隔，单位秒
     */
    private final int keyFrameInterval;

    /**
     * RTSP服务器主机地址
     */
    private String host = "0.0.0.0";
    
    /**
     * 构造函数，初始化RTSP配置对象
     * 
     * @param width 视频宽度，像素单位
     * @param height 视频高度，像素单位
     * @param frameRate 视频帧率，单位fps
     * @param videoBitrate 视频比特率，单位bps
     * @param port RTSP服务器端口
     * @param recordMic 是否录制麦克风音频
     * @param keyFrameInterval 关键帧间隔，单位秒
     */
    private RTSPConfig(int width, int height, int frameRate, int videoBitrate, int port, boolean recordMic, int keyFrameInterval) 
    {
        this.width = width;
        this.height = height;
        this.frameRate = frameRate;
        this.videoBitrate = videoBitrate;
        this.port = port;
        this.recordMic = recordMic;
        this.keyFrameInterval = keyFrameInterval;
    }
    
    /**
     * 获取视频宽度
     * 
     * @return 视频宽度，像素单位
     */
    public int getWidth() 
    {
        return width;
    }
    
    /**
     * 获取视频高度
     * 
     * @return 视频高度，像素单位
     */
    public int getHeight() 
    {
        return height;
    }
    
    /**
     * 获取视频帧率
     * 
     * @return 视频帧率，单位fps
     */
    public int getFrameRate() 
    {
        return frameRate;
    }
    
    /**
     * 获取视频比特率
     * 
     * @return 视频比特率，单位bps
     */
    public int getVideoBitrate() 
    {
        return videoBitrate;
    }
    
    /**
     * 获取RTSP服务器端口
     * 
     * @return RTSP服务器端口号
     */
    public int getPort() 
    {
        return port;
    }
    
    /**
     * 判断是否录制麦克风音频
     * 
     * @return true表示录制麦克风，false表示录制系统声音
     */
    public boolean isRecordMic() 
    {
        return recordMic;
    }
    
    /**
     * 获取关键帧间隔
     * 
     * @return 关键帧间隔，单位秒
     */
    public int getKeyFrameInterval() 
    {
        return keyFrameInterval;
    }

    /**
     * 获取RTSP服务器主机地址
     * 
     * @return RTSP服务器主机地址
     */
    public String getHost() 
    {
        return host;
    }

    /**
     * 设置RTSP服务器主机地址
     * 
     * @param host RTSP服务器主机地址
     */
    public void setHost(String host) 
    {
        this.host = host;
    }
    
    /**
     * 创建默认配置
     * 
     * @return 默认RTSP配置实例
     */
    public static RTSPConfig createDefaultConfig() 
    {
        return new Builder().build();
    }
    
    /**
     * 构建器类，用于创建RTSPConfig实例
     * 采用Builder设计模式，提供流式API设置各项参数
     */
    public static class Builder 
    {
        /** 默认视频宽度 */
        private int width = 1920;
        /** 默认视频高度 */
        private int height = 1080;
        /** 默认帧率 */
        private int frameRate = 60;
        /** 默认比特率：8 Mbps */
        private int videoBitrate = 8 * 1000 * 1000;
        /** 默认RTSP端口 */
        private int port = 554;
        /** 默认不录制麦克风 */
        private boolean recordMic = false;
        /** 默认关键帧间隔 */
        private int keyFrameInterval = 1;
        
        /**
         * 默认构造函数，使用默认参数初始化
         */
        public Builder() 
        {
        }
        
        /**
         * 设置视频分辨率
         * 
         * @param width 宽度，像素单位
         * @param height 高度，像素单位
         * @return 构建器实例，用于链式调用
         */
        public Builder setResolution(int width, int height) 
        {
            this.width = width;
            this.height = height;
            return this;
        }
        
        /**
         * 使用预设分辨率
         * 
         * @param resolution 分辨率预设枚举值
         * @return 构建器实例，用于链式调用
         */
        public Builder setResolution(Resolution resolution) 
        {
            this.width = resolution.getWidth();
            this.height = resolution.getHeight();
            return this;
        }
        
        /**
         * 设置帧率
         * 
         * @param frameRate 帧率，单位fps
         * @return 构建器实例，用于链式调用
         */
        public Builder setFrameRate(int frameRate) 
        {
            this.frameRate = frameRate;
            return this;
        }
        
        /**
         * 设置视频比特率
         * 
         * @param videoBitrate 比特率，单位bps
         * @return 构建器实例，用于链式调用
         */
        public Builder setVideoBitrate(int videoBitrate) 
        {
            this.videoBitrate = videoBitrate;
            return this;
        }
        
        /**
         * 设置RTSP服务器端口
         * 
         * @param port 端口号
         * @return 构建器实例，用于链式调用
         */
        public Builder setPort(int port) 
        {
            this.port = port;
            return this;
        }
        
        /**
         * 设置是否录制麦克风音频
         * 
         * @param recordMic true表示录制麦克风，false表示录制系统声音
         * @return 构建器实例，用于链式调用
         */
        public Builder setRecordMic(boolean recordMic) 
        {
            this.recordMic = recordMic;
            return this;
        }
        
        /**
         * 设置关键帧间隔
         * 
         * @param keyFrameInterval 间隔，单位秒
         * @return 构建器实例，用于链式调用
         */
        public Builder setKeyFrameInterval(int keyFrameInterval) 
        {
            this.keyFrameInterval = keyFrameInterval;
            return this;
        }
        
        /**
         * 构建RTSPConfig实例
         * 
         * @return 根据当前配置构建的RTSPConfig实例
         */
        public RTSPConfig build() 
        {
            return new RTSPConfig(
                width,
                height,
                frameRate,
                videoBitrate,
                port,
                recordMic,
                keyFrameInterval
            );
        }
    }
    
    /**
     * 分辨率预设枚举
     * 提供常用分辨率选项
     */
    public enum Resolution 
    {
        /** 640x480分辨率(VGA) */
        RES_640x480(640, 480),
        /** 720x480分辨率(SD) */
        RES_720x480(720, 480),
        /** 1280x720分辨率(HD/720P) */
        RES_1280x720(1280, 720),
        /** 1920x1080分辨率(FHD/1080P) */
        RES_1920x1080(1920, 1080),
        /** 3840x2160分辨率(UHD/4K) */
        RES_3840x2160(3840, 2160);
        
        /** 宽度像素值 */
        private final int width;
        /** 高度像素值 */
        private final int height;
        
        /**
         * 构造函数
         * 
         * @param width 宽度，像素单位
         * @param height 高度，像素单位
         */
        Resolution(int width, int height) 
        {
            this.width = width;
            this.height = height;
        }
        
        /**
         * 获取分辨率宽度
         * 
         * @return 宽度，像素单位
         */
        public int getWidth() 
        {
            return width;
        }
        
        /**
         * 获取分辨率高度
         * 
         * @return 高度，像素单位
         */
        public int getHeight() 
        {
            return height;
        }
    }
}
