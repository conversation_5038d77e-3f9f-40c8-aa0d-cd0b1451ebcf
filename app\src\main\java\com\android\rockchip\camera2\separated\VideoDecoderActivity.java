package com.android.rockchip.camera2.separated;

import android.Manifest;
import android.content.Context;
import android.content.pm.PackageManager;
import android.os.Bundle;
import android.os.Handler;
import android.util.Log;
import android.view.Surface;
import android.view.TextureView;
import android.graphics.SurfaceTexture;
import android.view.GestureDetector;
import android.view.MotionEvent;
import android.view.ScaleGestureDetector;
import android.widget.Button;
import android.widget.SeekBar;
import android.widget.TextView;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.appcompat.app.AppCompatActivity;
import androidx.core.app.ActivityCompat;
import androidx.core.content.ContextCompat;

import com.touptek.ui.internal.TpViewTransform;
import com.touptek.utils.TpFileManager;
import com.touptek.video.internal.TpVideoDecoder;
import com.android.rockchip.mediacodecnew.R;

/**
 * VideoDecoderActivity 类负责视频解码和播放控制。
 */
public class VideoDecoderActivity extends AppCompatActivity
{
    /* 日志标签 */
    private static final String TAG = "TpVideoDecoder";

    /* 界面控件 */
    private TextureView textureView;
    private SeekBar seekBar;
    private Button playPauseButton;
    private Button frameByFrameButton;
    private Button fastForwardButton;
    private Button fastBackwardButton;
    private TextView tvDuration;
    private TextView tvCurrentPosition;
    private Button speedButton;

    /* 视频解码器实例 */
    private TpVideoDecoder tpVideoDecoder;

    /* 用于更新进度条的 Handler 和 Runnable */
    private final Handler handler = new Handler();
    private final Runnable updateSeekBarRunnable = new Runnable()
    {
        @Override
        public void run()
        {
            /* 若解码器存在且正在解码 */
            if (tpVideoDecoder != null && tpVideoDecoder.isDecoding())
            {
                /* 更新UI显示 */
                updatePlayerUI();

                /* 检查是否暂停或播放完成 */
                if (!tpVideoDecoder.isPaused() && !tpVideoDecoder.isPlaybackCompleted()) {
                    /* 每秒更新一次进度条 */
                    handler.postDelayed(this, 1000);
                }
            }
        }
    };

    /* 缩放手势检测器 */
    private ScaleGestureDetector scaleGestureDetector;

    /* 平移手势检测器 */
    private GestureDetector gestureDetector;

    /* 当前速度索引 */
    private int currentSpeedIndex = 0;
    /* 播放速度选项 */
    private final float[] playbackSpeeds = {1.0f, 1.5f, 2.0f, 0.5f, 0.25f};
    /* 播放速度选项标签 */
    private final String[] speedLabels = {"正常", "1.5x", "2x", "0.5x", "0.25x"};

    @Override
    protected void onCreate(Bundle savedInstanceState)
    {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.decoder);

        /* 初始化界面控件 */
        textureView = findViewById(R.id.texture_view);
        seekBar = findViewById(R.id.seek_bar);
        playPauseButton = findViewById(R.id.btn_play_pause);
        frameByFrameButton = findViewById(R.id.btn_step_decode);
        fastForwardButton = findViewById(R.id.btn_fast_forward);
        fastBackwardButton = findViewById(R.id.btn_fast_backward);
        tvDuration = findViewById(R.id.tv_duration);
        tvCurrentPosition = findViewById(R.id.tv_current_position);
        
        /* 初始化速度控制按钮 */
        speedButton = findViewById(R.id.btn_speed);
        if (speedButton != null)
        {
            speedButton.setText("速度：" + speedLabels[currentSpeedIndex]);
            speedButton.setOnClickListener(v -> togglePlaybackSpeed());
        }

        /* 请求存储权限 */
        requestStoragePermission();
        final Context context = this;

        /* 设置 TextureView 的 SurfaceTextureListener */
        textureView.setSurfaceTextureListener(new TextureView.SurfaceTextureListener()
        {
            @Override
            public void onSurfaceTextureAvailable(SurfaceTexture surface, int width, int height)
            {
                Log.d(TAG, "SurfaceTexture Available");
                /* 获取视频路径 */
                String videoPath = getIntent().getStringExtra("videoPath"); /* 获取传递的视频路径 */
                if (videoPath == null)
                {
                    videoPath = TpFileManager.createVideoPath(context); /* 默认路径 */
                }
                /* 初始化视频解码器，将解码输出绑定到 Surface */
                tpVideoDecoder = new TpVideoDecoder(videoPath, new Surface(surface));
                
                /* 设置播放结束监听器 */
                tpVideoDecoder.setPlaybackListener(new TpVideoDecoder.VideoDecoderListener()
                {
                    @Override
                    public void onPlaybackCompleted()
                    {
                        runOnUiThread(() ->
                        {
                            /* 更新UI，显示播放已完成的状态 */
                            updatePlayButton(false);
                            Toast.makeText(VideoDecoderActivity.this, "视频播放完毕", Toast.LENGTH_SHORT).show();
                        });
                    }
                });
                
                tpVideoDecoder.startDecoding();

                /* 设置初始播放速度 */
                tpVideoDecoder.setPlaybackSpeed(playbackSpeeds[currentSpeedIndex]);

                /* 开始更新进度条 */
                handler.post(updateSeekBarRunnable);
                /* 更新播放按钮文本 */
                updatePlayButton(true);
                /* 获取视频总时长并显示 */
                long duration = tpVideoDecoder.getVideoDuration();
                tvDuration.setText("总时长: " + formatTime(duration));
            }

            @Override
            public void onSurfaceTextureSizeChanged(SurfaceTexture surface, int width, int height) { }

            @Override
            public boolean onSurfaceTextureDestroyed(SurfaceTexture surface)
            {
                /* 停止解码 */
                if (tpVideoDecoder != null)
                {
                    tpVideoDecoder.stopDecoding();
                }
                /* 停止更新进度条 */
                handler.removeCallbacks(updateSeekBarRunnable);
                return true;
            }

            @Override
            public void onSurfaceTextureUpdated(SurfaceTexture surface) { }
        });

        /* 设置进度条监听器 */
        seekBar.setOnSeekBarChangeListener(new SeekBar.OnSeekBarChangeListener()
        {
            @Override
            public void onProgressChanged(SeekBar seekBar, int progress, boolean fromUser)
            {
                /* 若用户手动拖动进度条 */
                if (fromUser && tpVideoDecoder != null)
                {
                    /* 获取视频总时长 */
                    long videoDuration = tpVideoDecoder.getVideoDuration();
                    /* 跳转到指定位置 */
                    tpVideoDecoder.seekTo(videoDuration * progress / 100);
                    /* 立即更新UI显示 */
                    updatePlayerUI();
                }
            }

            @Override
            public void onStartTrackingTouch(SeekBar seekBar)
            {
                /* 空实现，可根据需求添加逻辑 */
            }

            @Override
            public void onStopTrackingTouch(SeekBar seekBar)
            {
                /* 空实现，可根据需求添加逻辑 */
            }
        });

        /* 设置播放/暂停按钮点击事件 */
        playPauseButton.setOnClickListener(v ->
        {
            if (tpVideoDecoder != null)
            {
                /* 切换播放/暂停状态 */
                tpVideoDecoder.togglePlayPause();
                /* 更新播放按钮文本 */
                updatePlayButton(!tpVideoDecoder.isPaused());
                if (!tpVideoDecoder.isPaused())
                {
                    /* 继续更新进度条 */
                    handler.post(updateSeekBarRunnable);
                }
            }
        });

        /* 设置逐帧播放按钮点击事件 */
        frameByFrameButton.setOnClickListener(v ->
        {
            if (tpVideoDecoder != null)
            {
                /* 调用逐帧播放方法 */
                tpVideoDecoder.stepFrame();
                /* 更新播放按钮文本 */
                updatePlayButton(false);
            }
        });

        /* 设置快进按钮点击事件 */
        fastForwardButton.setOnClickListener(v ->
        {
            if (tpVideoDecoder != null)
            {
                /* 快进5秒 */
                tpVideoDecoder.seekRelative(5000);
                /* 立即更新UI显示 */
                updatePlayerUI();
                
                /* 如果处于暂停状态，让解码器解码新位置的帧 */
                if (tpVideoDecoder.isPaused()) {
                    tpVideoDecoder.stepFrame();
                }
            }
        });

        /* 设置快退按钮点击事件 */
        fastBackwardButton.setOnClickListener(v ->
        {
            if (tpVideoDecoder != null)
            {
                /* 快退5秒 */
                tpVideoDecoder.seekRelative(-5000);
                /* 立即更新UI显示 */
                updatePlayerUI();
                
                /* 如果处于暂停状态，让解码器解码新位置的帧 */
                if (tpVideoDecoder.isPaused())
                {
                    tpVideoDecoder.stepFrame();
                }
            }
        });

        /* 初始化缩放和平移手势检测器 */
        initScaleGestureDetector();
        initPanGestureDetector();

        /* 设置触摸监听器 */
        textureView.setOnTouchListener((v, event) ->
        {
            boolean scaleHandled = scaleGestureDetector.onTouchEvent(event);
            boolean panHandled = gestureDetector.onTouchEvent(event);
            return scaleHandled || panHandled; /* 确保触摸事件被正确处理 */
        });
    }

    /**
     * 初始化缩放手势检测器
     */
    private void initScaleGestureDetector() {
        scaleGestureDetector = new ScaleGestureDetector(this, new ScaleGestureDetector.SimpleOnScaleGestureListener()
        {
            @Override
            public boolean onScale(ScaleGestureDetector detector)
            {
                float scaleFactor = detector.getScaleFactor();
                float focusX = detector.getFocusX();
                float focusY = detector.getFocusY();

                /* 调用 TpViewTransform 的 applyZoom 方法 */
                TpViewTransform.applyZoom(textureView, scaleFactor, focusX, focusY);
                return true;
            }
        });
    }

    /**
     * 初始化平移手势检测器
     */
    private void initPanGestureDetector()
    {
        gestureDetector = new GestureDetector(this, new GestureDetector.SimpleOnGestureListener() {
            @Override
            public boolean onScroll(MotionEvent e1, MotionEvent e2, float distanceX, float distanceY)
            {
                /* 调用 TpViewTransform 的 applyPan 方法 */
                TpViewTransform.applyPan(textureView, -distanceX, -distanceY);
                return true;
            }
        });
    }

    /**
     * 请求存储权限
     */
    private void requestStoragePermission()
    {
        /* 检查是否有存储权限 */
        if (ContextCompat.checkSelfPermission(this, Manifest.permission.READ_EXTERNAL_STORAGE) != PackageManager.PERMISSION_GRANTED)
        {
            /* 请求存储权限 */
            ActivityCompat.requestPermissions(this, new String[]{Manifest.permission.READ_EXTERNAL_STORAGE}, 1);
        }
    }

    @Override
    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions, @NonNull int[] grantResults)
    {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
        /* 检查权限请求结果 */
        if (requestCode == 1 && grantResults.length > 0 && grantResults[0] != PackageManager.PERMISSION_GRANTED)
        {
            Log.e(TAG, "Storage permission denied");
            /* 权限被拒绝，关闭活动 */
            finish();
        }
    }

    /**
     * 更新播放按钮文本
     * @param playing 是否正在播放
     */
    private void updatePlayButton(boolean playing)
    {
        /* 根据播放状态更新按钮文本 */
        playPauseButton.setText(playing ? "暂停" : "播放");
    }

    @Override
    protected void onDestroy()
    {
        super.onDestroy();
        /* 停止解码 */
        if (tpVideoDecoder != null)
        {
            tpVideoDecoder.stopDecoding();
        }
        /* 停止更新进度条 */
        handler.removeCallbacks(updateSeekBarRunnable);
    }

    /**
     * 格式化时间显示
     * @param timeUs 时间（微秒）
     * @return 格式化后的时间字符串
     */
    private String formatTime(long timeUs)
    {
        /* 转换为秒 */
        long totalSeconds = timeUs / 1000000;
        /* 计算小时 */
        long hours = totalSeconds / 3600;
        /* 计算分钟 */
        long minutes = (totalSeconds % 3600) / 60;
        /* 计算秒 */
        long seconds = totalSeconds % 60;
        return String.format("%02d:%02d:%02d", hours, minutes, seconds);
    }

    /**
     * 切换播放速度
     */
    private void togglePlaybackSpeed() {
        if (tpVideoDecoder != null)
        {
            /* 循环切换播放速度 */
            currentSpeedIndex = (currentSpeedIndex + 1) % playbackSpeeds.length;
            float newSpeed = playbackSpeeds[currentSpeedIndex];
            
            /* 更新UI显示 */
            speedButton.setText("速度：" + speedLabels[currentSpeedIndex]);
            
            /* 设置解码器的播放速度 */
            tpVideoDecoder.setPlaybackSpeed(newSpeed);
            
            Toast.makeText(this, "播放速度设置为: " + speedLabels[currentSpeedIndex], Toast.LENGTH_SHORT).show();
        }
    }

    /**
     * 更新播放器UI
     * 包括进度条位置和时间显示
     */
    private void updatePlayerUI() {
        if (tpVideoDecoder != null)
        {
            long currentPosition = tpVideoDecoder.getCurrentPosition();
            long duration = tpVideoDecoder.getVideoDuration();

            /* 更新当前时间显示 */
            tvCurrentPosition.setText("当前时长: " + formatTime(currentPosition));

            /* 更新进度条 */
            if (duration > 0 && currentPosition >= 0)
            {
                int progress = (int) (currentPosition * 100 / duration);
                /* 限制进度在有效范围内 */
                progress = Math.max(0, Math.min(100, progress));
                seekBar.setProgress(progress);
            }
        }
    }
}