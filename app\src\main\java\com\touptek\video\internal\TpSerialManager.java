package com.touptek.video.internal;

import android.os.Handler;
import android.os.Looper;
import android.util.Log;

import com.touptek.video.TpIspParam;

/**
 * TpSerialManager 类用于管理串口通信。
 * <p>
 * 此类提供了串口的更加底层的初始化、命令发送、状态监听等功能。
 * 主要职责包括：
 * 1. 提供串口设备的初始化和关闭
 * 2. 提供串口命令的发送
 * 3. 管理设备插拔状态监听
 * 4. 处理串口数据接收
 */
public class TpSerialManager
{
    /**
     * 静态代码块，加载本地库
     * 在类第一次被加载时执行
     */
    static 
    {
        // 加载本地库，添加jni后缀
        System.loadLibrary("touptek_serial_jni");
    }

    // JNI 方法声明，用于与底层C/C++代码交互
    /**
     * 初始化串口通信
     * @param baudRate 波特率，如9600、115200等
     * @return 成功返回1，失败返回0
     */
    private native int initSerial(int baudRate);
    
    /**
     * 发送命令到串口设备
     * @param ctrl 控制字节，0x00表示读操作，0x01表示写操作
     * @param command 命令字节，对应具体功能
     * @param data 包含4字节的数据数组
     */
    private static native void sendCommand(int ctrl, int command, int[] data);
    
    /**
     * 关闭串口通信
     * 释放相关资源
     */
    private native void closeSerial();

    /**
     * 开始串口设备检测。
     * <p>
     * 如果检测到串口设备插入，将自动初始化串口。
     * 此方法会启动一个监控线程来持续检测设备状态。
     *
     * @return 如果检测启动成功返回 1，否则返回 0。
     */
    public native int startMonitor();

    /**
     * 停止串口设备检测。
     * <p>
     * 停止所有与串口设备检测相关的操作。
     * 会中止监控线程并释放相关资源。
     */
    public native void stopMonitor();

    /**
     * 定义设备状态回调接口。
     * <p>
     * 当设备状态发生变化时，会触发此接口的回调方法。
     * 应用可以实现此接口来监听设备插拔事件。
     */
    public interface DeviceStateCallback 
    {
        /**
         * 当设备状态发生变化时调用。
         *
         * @param connected 设备是否已连接，true表示已连接，false表示已断开
         */
        void onDeviceStateChanged(boolean connected);
    }

    /**
     * 判断当前串口的连接状态。
     *
     * @return 如果串口已连接返回 true，否则返回 false。
     */
    public native boolean isSerialConnected();

    /**
     * 发送串口命令。
     * <p>
     * 将整数数据拆分为4个字节，并通过JNI发送到设备。
     *
     * @param ctrl    控制字节，0x00表示读操作，0x01表示写操作。
     * @param command 命令字节，对应{@link TpIspParam}中的枚举值。
     * @param data    整型数据，会被拆分为4个字节发送。
     */
    public static void sendCommandToSerial(int ctrl, int command, int data) 
    {
        int[] send_data= new int[4];

        // 将32位整数拆分为4个字节
        send_data[3] = (data & 0xFF);                 // 获取最低字节
        send_data[2] = ((data >> 8) & 0xFF);          // 获取第二个字节
        send_data[1] = ((data >> 16) & 0xFF);         // 获取第三个字节
        send_data[0] = ((data >> 24) & 0xFF);         // 获取最高字节
        
        // 记录日志，便于调试
//        Log.e("TAG", "sendCommandToSerial: " + send_data[0]);
//        Log.e("TAG", "sendCommandToSerial: " + send_data[1]);
//        Log.e("TAG", "sendCommandToSerial: " + send_data[2]);
//        Log.e("TAG", "sendCommandToSerial: " + send_data[3]);

        // 调用JNI方法发送命令
        sendCommand(ctrl, command, send_data);
    }

    /**
     * 初始化串口。
     * <p>
     * 设置串口通信参数并打开串口。
     *
     * @param baudRate 波特率，常用值有9600、115200等。
     * @return 如果初始化成功返回 true，否则返回 false。
     */
    public boolean initializeSerial(int baudRate) 
    {
        return initSerial(baudRate) == 1;
    }

    /**
     * 设备插入/拔出回调方法。
     * 当设备状态发生变化时由JNI层调用。
     *
     * @param connected 设备是否已连接，true表示已连接，false表示已断开。
     */
    public void onDeviceStateChanged(boolean connected) 
    {
        Log.d("SerialMonitor", "Device state changed: " + (connected ? "Connected" : "Disconnected"));
        if(connected)
        {
            // 使用Handler实现非阻塞延时
            new Handler(Looper.getMainLooper()).postDelayed(() -> {
                Log.d("SerialMonitor", "延时结束，开始发送参数到设备");
                TpIspParam.requestAllParamRanges();
                TpIspParam.syncAllCurrentValuesToDevice();

                // 简单初始化系统场景
                TpIspParam.initializeSystemScenes();
            }, 3000);  // 3000毫秒后执行
        }
        // 触发应用层回调
        if (deviceStateCallback != null) 
        {
            deviceStateCallback.onDeviceStateChanged(connected);
        }
    }

    // 设备状态回调变量
    private DeviceStateCallback deviceStateCallback;

    /**
     * 设置设备状态回调。
     * <p>
     * 当设备的连接状态发生变化时，将触发回调方法 {@link DeviceStateCallback#onDeviceStateChanged(boolean)}。
     * 应用层可以通过此回调获知设备的插拔状态。
     *
     * @param callback 回调接口实例，用于监听设备状态的变化。
     */
    public void setDeviceStateCallback(DeviceStateCallback callback) 
    {
        this.deviceStateCallback = callback;
    }

    /**
     * 串口数据接收回调。
     * <p>
     * 当串口接收到数据时自动调用，然后将接收到的数据存入TouptekIspParam中。
     * 用户需要使用变量可以在TouptekIspParam中获取。
     * 此方法由JNI层在接收到数据时调用。
     * </p>
     * 
     * @param data 接收到的数据数组，包含命令字节和参数值。
     */
    public void onSerialDataReceived(int[] data) 
    {
        // 数据长度至少需要6个字节
        if (data.length >= 6) 
        {
            int command = data[0]; // 第1个字节是命令

            // 获取对应的 TpIspParam
            TpIspParam param = TpIspParam.fromInt(command);
            if (param != null) 
            {
                if (param == TpIspParam.TOUPTEK_PARAM_VERSION)
                {
                    // 版本号需要特殊处理，使用long类型存储
                    long version = ((long) data[1] << 32) | ((long) data[2] << 24) |
                            ((long) data[3] << 16) | ((long) data[4] << 8) | data[5];
                    // 启用参数更新和回调
                    TpIspParam.handleReceivedData(param, version, true);
                    Log.d("TouptekSerial", "Received " + param.name() + " = " + version);
                }
                else
                {
                    int paramValue = (data[2] << 24) | (data[3] << 16) | (data[4] << 8) | data[5];
                    switch (data[1])
                    {
                        case 0:
                            // 启用参数更新和回调
                            TpIspParam.handleReceivedData(param, paramValue, false);
                            Log.d("TouptekSerial", "Received " + param.name() + " = " + paramValue);
                            break;
                        case 1:  //isDisable
                            boolean isDisable;
                            isDisable= paramValue != 0;   //如果paramValue == 0，为false 否者为ture
                            TpIspParam.setParamDisabled(param,isDisable);
                            Log.d("TouptekSerial", "Received " + param.name() + "isDisable = " + isDisable);
                            break;
                        case 2:  //iMin
                            TpIspParam.setParamMinValue(param,paramValue);
                            Log.d("TouptekSerial", "Received " + param.name() + "min = " + paramValue);
                            break;
                        case 3:  //iMax
                            TpIspParam.setParamMaxValue(param,paramValue);
                            Log.d("TouptekSerial", "Received " + param.name() + "max = " + paramValue);
                            break;
                        case 4:  //defaultValue
                            TpIspParam.setParamDefault(param,paramValue);
                            Log.d("TouptekSerial", "Received " + param.name() + "defaultValue = " + paramValue);
                            break;
                    }
                }
            } 
            else 
            {
                if (data[0] == 0xF0 || data[5] ==1)
                {
                    TpIspParam.setParamsRangeReceived(true);
                    Log.d("TouptekSerial", "Received succeed\n");
                }
                else
                {
                    Log.e("TouptekSerial", "Unknown command: 0x" + Integer.toHexString(command));
                }

            }
        }
    }

    /**
     * 关闭串口。
     * <p>
     * 释放串口资源，应在不再需要串口通信时调用。
     */
    public void close() 
    {
        closeSerial();
    }
}
