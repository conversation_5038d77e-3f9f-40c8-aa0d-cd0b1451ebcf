package com.touptek.video.internal;

import android.media.MediaCodec;
import android.media.MediaExtractor;
import android.media.MediaFormat;
import android.util.Log;
import android.view.Surface;

import java.io.IOException;
import java.nio.ByteBuffer;

/**
 * TpVideoDecoder 类负责解码视频文件并将其渲染到给定的 Surface 上。
 * <p>
 * 此类提供了视频解码的初始化、播放控制（播放、暂停、逐帧播放）、跳转以及资源释放等功能。
 * </p>
 * 
 * <p><b>主要功能：</b></p>
 * <ul>
 *   <li>视频文件解码和渲染</li>
 *   <li>播放控制（播放、暂停、继续）</li>
 *   <li>逐帧播放模式</li>
 *   <li>精确的时间和位置控制</li>
 *   <li>可调节的播放速度</li>
 *   <li>播放状态和完成回调</li>
 * </ul>
 * 
 * <p><b>使用示例：</b></p>
 * <pre>{@code
 * // 创建解码器
 * Surface surface = textureView.getSurface();
 * TpVideoDecoder decoder = new TpVideoDecoder("/storage/videos/sample.mp4", surface);
 * 
 * // 设置播放完成监听器
 * decoder.setPlaybackListener(() -> {
 *     Log.d(TAG, "视频播放完成");
 * });
 * 
 * // 开始解码
 * decoder.startDecoding();
 * 
 * // 播放控制
 * decoder.togglePlayPause();  // 切换播放/暂停
 * decoder.seekTo(5000);       // 跳转到5秒位置
 * decoder.setPlaybackSpeed(1.5f); // 设置1.5倍速播放
 * 
 * // 停止解码并释放资源
 * decoder.stopDecoding();
 * }</pre>
 */
public class TpVideoDecoder
{
    private static final String TAG = "TpVideoDecoder";
    private final String videoPath;
    private final Surface surface;

    private MediaCodec mediaCodec;
    private MediaExtractor mediaExtractor;
    private Thread decodingThread;

    private volatile boolean isDecoding = false;
    private volatile boolean isPaused = false;
    private volatile boolean isFrameByFrame = false;
    /* 添加播放结束标志 */
    private volatile boolean isPlaybackCompleted = false;

    private long videoDuration;

    /* 添加回调接口 */
    private VideoDecoderListener videoDecoderListener;

    /**
     * 播放状态回调接口
     */
    public interface VideoDecoderListener {
        /**
         * 播放完成时调用
         */
        void onPlaybackCompleted();
    }

    /**
     * 设置播放监听器
     * @param listener 播放监听器
     */
    public void setPlaybackListener(VideoDecoderListener listener) {
        this.videoDecoderListener = listener;
    }

    /* 增强播放速度控制的变量 */
    private long lastFrameTimeUs = 0;
    private float playbackSpeed = 1.0f; /* 播放速度因子，1.0表示正常速度 */
    private long startTimeMs = 0; /* 开始播放的系统时间 */
    private long startMediaTimeUs = 0; /* 开始播放的媒体时间*/
    private boolean timeBaseNeedsReset = true; /* 时间基准是否需要重置 */

    /**
     * 构造函数。
     * <p>
     * 初始化 TpVideoDecoder 实例并设置视频文件路径和渲染 Surface。
     * </p>
     *
     * @param videoPath
     *        视频文件的路径。
     * @param surface
     *        用于渲染视频的 Surface。
     */
    public TpVideoDecoder(String videoPath, Surface surface)
    {
        this.videoPath = videoPath;
        this.surface = surface;

        /* 打印日志 */
        Log.d(TAG, "TpVideoDecoder");
    }

    /**
     * 开始视频解码过程。
     * <p>
     * 此方法会初始化 MediaExtractor 和 MediaCodec，并启动解码线程。
     * 解码后的视频帧会被渲染到指定的 Surface 上。
     * </p>
     */
    public synchronized void startDecoding()
    {
        try
        {
            /* 创建媒体提取器实例 */
            mediaExtractor = new MediaExtractor();
            /* 设置视频文件数据源 */
            mediaExtractor.setDataSource(videoPath);

            /* 选择视频轨道索引 */
            int trackIndex = selectVideoTrack(mediaExtractor);
            if (trackIndex == -1)
            {
                Log.e(TAG, "未找到视频轨道");
                return;
            }

            /* 获取视频轨道索引的媒体格式 */
            MediaFormat format = mediaExtractor.getTrackFormat(trackIndex);
            /* 获取视频帧的总时长(us) */
            videoDuration = format.getLong(MediaFormat.KEY_DURATION);
            /* 获取视频编码格式（如：video/avc） */
            String mime = format.getString(MediaFormat.KEY_MIME);
            /* 根据 mime 类型创建对应的解码器 */
            mediaCodec = MediaCodec.createDecoderByType(mime);
            /* 配置解码器（输出到指定 surface） */
            mediaCodec.configure(format, surface, null, 0);
            /* 启动解码器 */
            mediaCodec.start();

            /* 重置播放结束标志 */
            isPlaybackCompleted = false;
            /* 重置时间基准标志 */
            timeBaseNeedsReset = true;

            /* 启动解码线程 */
            isDecoding = true;
            /* 创建并启动解码器线程 */
            decodingThread = new Thread(this::decodingTask);
            decodingThread.start();
        }
        catch (IOException e)
        {
            Log.e(TAG, "解码器初始化失败", e);
        }

        /* 打印日志 */
        Log.d(TAG, "startDecoding");
    }

    /**
     * 选择媒体文件中的视频轨道。
     * <p>
     * 此方法会遍历媒体文件中的所有轨道，找到第一个视频轨道并返回其索引。
     * 如果未找到视频轨道，则返回 -1。
     * </p>
     *
     * @param extractor
     *        媒体提取器实例，用于访问媒体轨道。
     * @return
     *        视频轨道的索引。如果未找到视频轨道，则返回 -1。
     */
    private int selectVideoTrack(MediaExtractor extractor)
    {
        /* 遍历所有可用媒体轨道 */
        for (int i = 0; i < extractor.getTrackCount(); i++)
        {
            /* 获取当前轨道的媒体格式 */
            MediaFormat format = extractor.getTrackFormat(i);

            /* 检测是否为视频轨道（MIME 类型以 "video/" 开头） */
            if (format.getString(MediaFormat.KEY_MIME).startsWith("video/"))
            {
                /* 选择该轨道进行后续操作 */
                extractor.selectTrack(i);
                /* 打印日志 */
                Log.d(TAG, "selectVideoTrack");
                return i; /* 返回找到的视频轨道索引 */
            }
        }

        /* 打印日志 */
        Log.d(TAG, "selectVideoTrack");
        return -1; /* 未找到符合条件的视频轨道 */
    }

    /**
     * 在单独线程中运行的主要解码任务。
     * <p>
     * 此方法会持续从媒体提取器读取数据，并将其传递给解码器进行解码。
     * 解码后的帧会被渲染到指定的 Surface 上。
     * </p>
     */
    private void decodingTask()
    {
        /* 创建输出缓冲区信息对象 */
        MediaCodec.BufferInfo bufferInfo = new MediaCodec.BufferInfo();

        /* 主解码循环：持续运行直到停止解码 */
        while (isDecoding)
        {
            /* 处理暂停/逐帧模式：短暂休眠避免 CPU 空转 */
            if (isPaused || isFrameByFrame)
            {
                safeSleep(10);
                continue;
            }

            /* 如果需要重置时间基准 */
            if (timeBaseNeedsReset) {
                startTimeMs = System.currentTimeMillis();
                startMediaTimeUs = 0; /* 会在接收到第一帧时设置 */
                lastFrameTimeUs = 0;
                timeBaseNeedsReset = false;
                Log.d(TAG, "时间基准已重置");
            }

            /* 从解码器获取可用输入缓冲区 */
            int inputIndex = mediaCodec.dequeueInputBuffer(10_000);
            if (inputIndex >= 0)
            {
                /* 获取输入缓冲区字节缓冲 */
                ByteBuffer inputBuffer = mediaCodec.getInputBuffer(inputIndex);
                /* 读取媒体数据到缓冲区 */
                int sampleSize = mediaExtractor.readSampleData(inputBuffer, 0);

                if (sampleSize < 0)
                {
                    /* 小于0表示视频流结束,发送流结束标志 */
                    mediaCodec.queueInputBuffer(inputIndex, 0, 0, 0, MediaCodec.BUFFER_FLAG_END_OF_STREAM);
                }
                else
                {
                    /* 获取当前样本的时间戳 */
                    long sampleTimeUs = mediaExtractor.getSampleTime();

                    /* 如果是第一帧，记录起始媒体时间 */
                    if (startMediaTimeUs == 0) {
                        startMediaTimeUs = sampleTimeUs;
                        Log.d(TAG, "设置起始媒体时间: " + startMediaTimeUs);
                    }

                    /* 提交有效数据到解码器 */
                    mediaCodec.queueInputBuffer(inputIndex, 0, sampleSize, sampleTimeUs, 0);
                    /* 推进到下一帧数据 */
                    mediaExtractor.advance();
                }
            }

            /* 从解码器获取输出缓冲区 */
            int outputIndex = mediaCodec.dequeueOutputBuffer(bufferInfo, 10_000);
            if (outputIndex >= 0)
            {
                /* 时间控制 - 确保按照原始帧率播放 */
                if (lastFrameTimeUs > 0 && bufferInfo.presentationTimeUs > lastFrameTimeUs)
                {
                    /* 计算从开始播放到当前帧应该经过的时间(毫秒) */
                    long elapsedMediaTimeUs = bufferInfo.presentationTimeUs - startMediaTimeUs;
                    long expectedRealTimeMs = (long)(elapsedMediaTimeUs / (1000 * playbackSpeed));

                    /* 计算实际已经过去的时间(毫秒) */
                    long realElapsedTimeMs = System.currentTimeMillis() - startTimeMs;

                    /* 如果实际时间小于应该经过的时间，需要等待 */
                    if (realElapsedTimeMs < expectedRealTimeMs)
                    {
                        long sleepTimeMs = expectedRealTimeMs - realElapsedTimeMs;
                        /* 避免休眠时间过长或过短 */
                        if (sleepTimeMs > 0 && sleepTimeMs < 500)
                        {
                            safeSleep(sleepTimeMs);
                            /* Log.v(TAG, "睡眠时间: " + sleepTimeMs + "ms, 播放速度: " + playbackSpeed); */
                        }
                    }
                    else if (realElapsedTimeMs > expectedRealTimeMs + 1000)
                    {
                        /* 实际时间远超预期，可能是因为暂停或系统负载，重置时间基准 */
                        Log.d(TAG, "时间偏差过大，重置时间基准");
                        timeBaseNeedsReset = true;
                    }
                }

                /* 更新上一帧的时间戳 */
                lastFrameTimeUs = bufferInfo.presentationTimeUs;

                /* 检查是否是流结束标志 */
                boolean isEos = (bufferInfo.flags & MediaCodec.BUFFER_FLAG_END_OF_STREAM) != 0;

                /* 渲染解码完成的帧到 Surface */
                mediaCodec.releaseOutputBuffer(outputIndex, bufferInfo.size > 0);

                /* 如果检测到流结束标志，标记播放完成并通知监听器 */
                if (isEos)
                {
                    Log.d(TAG, "检测到视频播放结束");
                    isPlaybackCompleted = true;
                    isPaused = true;

                    /* 通知监听器播放完成 */
                    if (videoDecoderListener != null)
                    {
                        videoDecoderListener.onPlaybackCompleted();
                    }
                }
            }
            else if (outputIndex == MediaCodec.INFO_OUTPUT_FORMAT_CHANGED)
            {
                /* 处理输出格式变化事件 */
                Log.d(TAG, "输出格式已更改");
            }
        }

        /* 打印日志 */
        Log.d(TAG, "decodingTask");
    }

    /**
     * 跳转到视频的指定位置。
     * <p>
     * 此方法会将解码器和提取器的状态重置到指定的时间位置。
     * 跳转位置会自动对齐到最近的关键帧。
     * </p>
     *
     * @param position
     *        要跳转到的位置，以微秒为单位。
     */
    public synchronized void seekTo(long position)
    {
        if (!isDecoding)
        {
            return;
        }

        boolean wasPaused = isPaused;
        isPaused = true;

        safeSleep(50);

        try
        {
            /* 使用Math.max(0, position)确保跳转位置不小于0,MediaExtractor.SEEK_TO_CLOSEST_SYNC表示跳转到最近的关键帧 */
            mediaExtractor.seekTo(Math.max(0, position), MediaExtractor.SEEK_TO_CLOSEST_SYNC);
            /* 清空解码器的输入和输出缓冲区 */
            mediaCodec.flush();

            /* 重置播放完成标志和时间基准 */
            isPlaybackCompleted = false;
            timeBaseNeedsReset = true;

            Log.d(TAG, "跳转到: " + position + "us, 并重置时间基准");
        }
        catch (IllegalStateException e)
        {
            Log.e(TAG, "跳转失败", e);
        }

        if(wasPaused)
        {
            togglePlayPause();
            safeSleep(50); /* 延时50ms */
        }

        /* 恢复到跳转前的解码状态 */
        isPaused = wasPaused;

        /* 打印日志 */
        Log.d(TAG, "seekTo: " + position);
    }

    /**
     * 切换播放和暂停状态。
     * <p>
     * 如果当前处于播放状态，则暂停播放；如果当前处于暂停状态，则恢复播放。
     * 如果播放已完成，则重置到开始位置并开始播放。
     * </p>
     */
    public void togglePlayPause()
    {
        /* 如果视频播放已完成，需要重置到起始位置 */
        if (isPlaybackCompleted)
        {
            resetToStart();
            isPaused = false;
            Log.d(TAG, "播放完成后重置到起始位置并播放");
        }
        else
        {
            /* 如果从暂停状态恢复播放，需要重置时间基准 */
            if (isPaused)
            {
                timeBaseNeedsReset = true;
                Log.d(TAG, "从暂停恢复播放，标记重置时间基准");
            }
            isPaused = !isPaused;
            Log.d(TAG, "切换播放/暂停状态: " + (isPaused ? "暂停" : "播放"));
        }
    }

    /**
     * 逐帧播放视频。
     * <p>
     * 此方法会解码并渲染视频的单个帧，然后暂停解码。
     * </p>
     */
    public void stepFrame()
    {
        /* 如果当前未暂停，则先暂停播放 */
        if (!isPaused)
        {
            togglePlayPause(); /* 切换到暂停状态 */
            safeSleep(100);    /* 确保解码器切换到暂停状态 */
        }

        /* 解码并渲染单帧 */
        new Thread(() ->
        {
            decodeSingleFrame();
        }).start();

        /* 打印日志 */
        Log.d(TAG, "stepFrame");
    }

    /**
     * 解码视频的单个帧。
     * <p>
     * 此方法会从解码器请求一个输出缓冲区，并将解码后的帧渲染到指定的 Surface 上。
     * </p>
     */
    private void decodeSingleFrame()
    {
        MediaCodec.BufferInfo bufferInfo = new MediaCodec.BufferInfo();

        int inputIndex = mediaCodec.dequeueInputBuffer(10_000);
        if (inputIndex >= 0)
        {
            /*从解码器中请求一个空闲的输入缓冲区 */
            ByteBuffer inputBuffer = mediaCodec.getInputBuffer(inputIndex);

            /* 将视频数据从mediaExtractor读取到输入缓冲区中 */
            int sampleSize = mediaExtractor.readSampleData(inputBuffer, 0);
            if (sampleSize >= 0)
            {
                /* 将填充好的输入缓冲区提交给解码器 */
                mediaCodec.queueInputBuffer(inputIndex, 0, sampleSize, mediaExtractor.getSampleTime(), 0);
                mediaExtractor.advance();
            }
        }

        /* 从解码器请求一个输出缓冲区 */
        int outputIndex = mediaCodec.dequeueOutputBuffer(bufferInfo, 10_000);
        if (outputIndex >= 0)
        {
            /* 将解码后的帧渲染到界面上 */
            mediaCodec.releaseOutputBuffer(outputIndex, true);
        }

        /* 打印日志 */
        Log.d(TAG, "decodeSingleFrame");
    }

    /**
     * 相对跳转控制方法。
     * <p>
     * 此方法会基于当前播放位置进行时间偏移跳转。
     * </p>
     *
     * @param deltaMs
     *        时间偏移量（单位：毫秒，正数表示前进，负数表示后退）。
     */
    public void seekRelative(long deltaMs)
    {
        try
        {
            long currentPos = mediaExtractor.getSampleTime();
            long newPos = Math.max(0, Math.min(videoDuration, currentPos + deltaMs * 1000));
            seekTo(newPos);
        }
        catch (IllegalStateException e)
        {
            Log.e(TAG, "跳转失败", e);
        }

        /* 打印日志 */
        Log.d(TAG, "seekRelative");
    }

    /**
     * 停止视频解码过程。
     * <p>
     * 此方法会停止解码线程并释放解码器和提取器的资源。
     * </p>
     */
    public synchronized void stopDecoding()
    {
        isDecoding = false;

        try
        {
            if (decodingThread != null)
            {
                decodingThread.join(500);
            }
        }
        catch (InterruptedException e)
        {
            Log.w(TAG, "线程中断", e);
        }

        if (mediaCodec != null)
        {
            mediaCodec.stop();
            mediaCodec.release();
            mediaCodec = null;
        }

        if (mediaExtractor != null)
        {
            mediaExtractor.release();
            mediaExtractor = null;
        }

        /* 打印日志 */
        Log.d(TAG, "stopDecoding");
    }

    /**
     * 检查视频是否正在解码。
     *
     * @return
     *        如果正在解码则返回 true，否则返回 false。
     */
    public boolean isDecoding()
    {
        /* 打印日志 */
        Log.d(TAG, "isDecoding");
        return isDecoding;
    }

    /**
     * 检查视频是否处于暂停状态。
     *
     * @return
     *        如果暂停则返回 true，否则返回 false。
     */
    public boolean isPaused()
    {
        /* 打印日志 */
        Log.d(TAG, "isPaused");
        return isPaused;
    }

    /**
     * 检查视频是否处于逐帧模式。
     *
     * @return
     *        如果处于逐帧模式则返回 true，否则返回 false。
     */
    public boolean isFrameByFrame()
    {
        /* 打印日志 */
        Log.d(TAG, "isFrameByFrame");
        return isFrameByFrame;
    }

    /**
     * 获取视频的持续时间。
     *
     * @return
     *        视频的持续时间，以微秒为单位。
     */
    public long getVideoDuration()
    {
        /* 打印日志 */
        Log.d(TAG, "getVideoDuration");
        return videoDuration;
    }

    /**
     * 获取视频的当前位置。
     *
     * @return
     *        视频的当前位置，以微秒为单位。
     */
    public long getCurrentPosition()
    {
        /* 打印日志 */
        Log.d(TAG, "getCurrentPosition");
        return mediaExtractor.getSampleTime();
    }

    /**
     * 安全地睡眠指定时间。
     * <p>
     * 此方法会捕获线程中断异常，确保线程在中断时不会崩溃。
     * </p>
     *
     * @param millis
     *        要睡眠的时间，以毫秒为单位。
     */
    private void safeSleep(long millis)
    {
        try
        {
            Thread.sleep(millis);
        }
        catch (InterruptedException e)
        {
            Log.d(TAG, "线程睡眠被中断");
        }

        /* 打印日志 */
        /* Log.d(TAG, "safeSleep"); */
    }

    /**
     * 设置播放速度
     *
     * @param speed 播放速度倍率，1.0表示正常速度
     */
    public void setPlaybackSpeed(float speed) {
        if (speed > 0) {
            /* 只有当速度真正改变时才重置时间基准 */
            if (this.playbackSpeed != speed) {
                this.playbackSpeed = speed;
                /* 标记需要重置时间基准，会在下一次解码循环中重置 */
                timeBaseNeedsReset = true;
                Log.d(TAG, "设置播放速度: " + speed + ", 并标记重置时间基准");
            }
        }
    }

    /**
     * 获取当前播放速度
     *
     * @return 当前播放速度倍率
     */
    public float getPlaybackSpeed() {
        return playbackSpeed;
    }

    /**
     * 重置播放到开始位置
     */
    public void resetToStart() {
        seekTo(0);
        isPlaybackCompleted = false;
    }

    /**
     * 检查视频播放是否已完成
     * @return 如果播放完成返回true，否则返回false
     */
    public boolean isPlaybackCompleted() {
        return isPlaybackCompleted;
    }
}