package com.touptek.video.internal.rtsp.service;

import android.content.Context;
import android.content.Intent;
import android.media.projection.MediaProjection;
import android.media.projection.MediaProjectionManager;

/**
 * 投影数据封装类
 * 封装了MediaProjection所需的数据
 */
public class ProjectionData {
    private final int resultCode;
    private final Intent data;
    
    public ProjectionData(int resultCode, Intent data) {
        this.resultCode = resultCode;
        this.data = data;
    }
    
    /**
     * 检查投影数据是否有效
     * @return 如果数据有效返回true
     */
    public boolean isValid() {
        return resultCode == android.app.Activity.RESULT_OK && data != null;
    }
    
    /**
     * 获取MediaProjection实例
     * @param context 上下文
     * @return MediaProjection实例，如果请求被拒绝则返回null
     */
    public MediaProjection getMediaProjection(Context context) {
        if (!isValid()) {
            return null;
        }
        
        MediaProjectionManager projectionManager = 
            (MediaProjectionManager) context.getSystemService(Context.MEDIA_PROJECTION_SERVICE);
        return projectionManager.getMediaProjection(resultCode, data);
    }
    
    /**
     * 获取结果代码
     * @return 结果代码
     */
    public int getResultCode() {
        return resultCode;
    }
    
    /**
     * 获取Intent数据
     * @return Intent数据
     */
    public Intent getData() {
        return data;
    }
}
