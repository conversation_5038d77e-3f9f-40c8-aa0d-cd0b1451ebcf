package com.android.rockchip.camera2.integrated.settings;

import android.content.Intent;
import android.os.Bundle;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.Window;
import android.view.WindowManager;
import android.widget.ImageButton;

import androidx.activity.result.ActivityResultLauncher;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.DialogFragment;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentManager;
import androidx.fragment.app.FragmentTransaction;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;


import com.android.rockchip.camera2.integrated.settings.adapters.SettingsMenuAdapter;
import com.android.rockchip.camera2.integrated.settings.fragments.TpSmbSettingsFragment;
import com.android.rockchip.camera2.integrated.settings.fragments.TpTvSettingsFragment;
import com.android.rockchip.camera2.integrated.settings.models.SettingsMenuItem;
import com.android.rockchip.camera2.integrated.settings.fragments.TpNetworkSettingsFragment;
import com.touptek.utils.TpSambaClient;
import com.touptek.video.TpVideoSystem;
import com.android.rockchip.mediacodecnew.R;

import java.util.ArrayList;
import java.util.List;

/**
 * ToupTek 设置对话框
 * 采用两面板架构：左侧导航菜单 + 右侧动态内容区域
 * 基于DialogFragment实现，不影响MainActivity生命周期
 */
public class TpSettingsDialog extends DialogFragment implements SettingsMenuAdapter.OnMenuItemClickListener {
    
    private static final String TAG = "TpSettingsDialog";
    
    // UI组件
    private RecyclerView settingsMenuRecycler;
    private ImageButton closeButton;
    private SettingsMenuAdapter menuAdapter;
    
    // 数据
    private List<SettingsMenuItem> menuItems;
    private SettingsMenuItem currentSelectedItem;
    
    // Fragment管理
    private FragmentManager fragmentManager;
    
    // 依赖对象
    private TpVideoSystem videoSystem;
    private ActivityResultLauncher<Intent> wifiPanelLauncher;
    private TpSambaClient tpSambaClient;

    /**
     * 创建TpSettingsDialog实例的工厂方法
     */
    public static TpSettingsDialog newInstance(TpVideoSystem videoSystem,
                                              ActivityResultLauncher<Intent> wifiPanelLauncher,
                                              TpSambaClient tpSambaClient) {
        TpSettingsDialog dialog = new TpSettingsDialog();
        dialog.videoSystem = videoSystem;
        dialog.wifiPanelLauncher = wifiPanelLauncher;
        dialog.tpSambaClient = tpSambaClient;
        return dialog;
    }
    
    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container,
                           @Nullable Bundle savedInstanceState) {
        View view = inflater.inflate(R.layout.dialog_tp_settings, container, false);

        // 初始化UI组件
        initViews(view);

        // 初始化菜单数据
        initMenuData();

        // 设置菜单适配器
        setupMenuAdapter();

        // 设置事件监听器
        setupEventListeners();

        Log.d(TAG, "TpSettingsDialog onCreateView 完成");
        return view;
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);

        // 设置Dialog窗口属性
        setupDialogWindow();

        // 使用childFragmentManager管理内部Fragment
        fragmentManager = getChildFragmentManager();

        // 默认选择第一个菜单项
        if (!menuItems.isEmpty()) {
            selectMenuItem(menuItems.get(0));
        }

        Log.d(TAG, "TpSettingsDialog 初始化完成");
    }
    
    /**
     * 设置Dialog窗口属性
     */
    private void setupDialogWindow() {
        if (getDialog() != null && getDialog().getWindow() != null) {
            Window window = getDialog().getWindow();
            // 设置Dialog大小为屏幕的80%宽度和70%高度
            WindowManager.LayoutParams params = window.getAttributes();
            params.width = (int) (getResources().getDisplayMetrics().widthPixels * 0.8);
            params.height = (int) (getResources().getDisplayMetrics().heightPixels * 0.7);
            window.setAttributes(params);

            // 设置背景为半透明
            window.setDimAmount(0.5f);
        }
    }
    
    /**
     * 初始化UI组件
     */
    private void initViews(View view) {
        settingsMenuRecycler = view.findViewById(R.id.settings_menu_recycler);
        closeButton = view.findViewById(R.id.btn_close_settings);
    }
    
    /**
     * 初始化菜单数据
     */
    private void initMenuData() {
        menuItems = new ArrayList<>();
        
        // 添加网络设置菜单项
        menuItems.add(new SettingsMenuItem(
            SettingsMenuItem.MenuType.NETWORK_SETTINGS,
            "网络设置",
            android.R.drawable.ic_menu_manage,
            "WiFi、以太网、热点、RTSP推流设置"
        ));

        // 添加SMB网络共享设置菜单项
        menuItems.add(new SettingsMenuItem(
            SettingsMenuItem.MenuType.SMB_SETTINGS,
            "SMB网络共享",
            android.R.drawable.ic_menu_share,
            "SMB服务器连接、文件共享、传输管理"
        ));

        // 添加TV模式设置菜单项
        menuItems.add(new SettingsMenuItem(
            SettingsMenuItem.MenuType.TV_MODE_SETTINGS,
            "TV模式设置",
            android.R.drawable.ic_menu_view,
            "TV模式和Camera模式切换、预览设置"
        ));

        // 预留其他设置分类（后续扩展）
        // menuItems.add(new SettingsMenuItem(
        //     SettingsMenuItem.MenuType.IMAGE_SETTINGS,
        //     "图像设置",
        //     android.R.drawable.ic_menu_camera,
        //     "分辨率、帧率、图像质量设置"
        // ));
        
        Log.d(TAG, "菜单数据初始化完成，共 " + menuItems.size() + " 个菜单项");
    }
    
    /**
     * 设置菜单适配器
     */
    private void setupMenuAdapter() {
        menuAdapter = new SettingsMenuAdapter(menuItems, this);
        settingsMenuRecycler.setLayoutManager(new LinearLayoutManager(getContext()));
        settingsMenuRecycler.setAdapter(menuAdapter);
    }
    
    /**
     * 设置事件监听器
     */
    private void setupEventListeners() {
        // 关闭按钮点击事件
        closeButton.setOnClickListener(v -> dismiss());

        // 点击Dialog外部区域关闭
        if (getDialog() != null) {
            getDialog().setCanceledOnTouchOutside(true);
        }
    }
    
    /**
     * 菜单项点击处理
     */
    @Override
    public void onMenuItemClick(SettingsMenuItem item) {
        selectMenuItem(item);
    }
    
    /**
     * 选择菜单项并显示对应的Fragment
     */
    private void selectMenuItem(SettingsMenuItem item) {
        Log.d(TAG, "选择菜单项: " + item.getTitle());
        
        // 更新选中状态
        if (currentSelectedItem != null) {
            currentSelectedItem.setSelected(false);
        }
        item.setSelected(true);
        currentSelectedItem = item;
        
        // 刷新菜单显示
        menuAdapter.notifyDataSetChanged();
        
        // 显示对应的Fragment
        showFragment(item.getType());
    }
    
    /**
     * 根据菜单类型显示对应的Fragment
     */
    private void showFragment(SettingsMenuItem.MenuType menuType) {
        if (fragmentManager == null) {
            Log.e(TAG, "FragmentManager为null，无法显示Fragment");
            return;
        }
        
        Fragment fragment = null;
        String tag = null;
        
        switch (menuType) {
            case NETWORK_SETTINGS:
                fragment = TpNetworkSettingsFragment.newInstance(videoSystem, wifiPanelLauncher);
                tag = "NetworkSettings";
                break;

            case SMB_SETTINGS:
                fragment = TpSmbSettingsFragment.newInstance(tpSambaClient);
                tag = "SmbSettings";
                break;

            case TV_MODE_SETTINGS:
                fragment = TpTvSettingsFragment.newInstance(videoSystem);
                tag = "TvSettings";
                break;

            // 预留其他Fragment
            // case IMAGE_SETTINGS:
            //     fragment = new TpImageSettingsFragment();
            //     tag = "ImageSettings";
            //     break;

            default:
                Log.w(TAG, "未知的菜单类型: " + menuType);
                return;
        }
        
        if (fragment != null) {
            // 隐藏默认提示内容
            View defaultContent = getView().findViewById(R.id.default_content);
            if (defaultContent != null) {
                defaultContent.setVisibility(View.GONE);
            }

            // 替换Fragment
            FragmentTransaction transaction = fragmentManager.beginTransaction();
            transaction.replace(R.id.settings_content_frame, fragment, tag);
            transaction.commit();

            Log.d(TAG, "Fragment切换完成: " + tag);
        }
    }
    
    @Override
    public void onStart() {
        super.onStart();
        Log.d(TAG, "TpSettingsDialog onStart");
    }

    @Override
    public void onStop() {
        super.onStop();
        Log.d(TAG, "TpSettingsDialog onStop");
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        Log.d(TAG, "TpSettingsDialog onDestroy");
    }
}
