package com.android.rockchip.camera2.integrated.browser;

import android.content.Intent;
import android.os.Bundle;
import android.widget.Toast;

import androidx.appcompat.app.AlertDialog;
import androidx.appcompat.app.AppCompatActivity;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.touptek.utils.TpFileManager;
import com.touptek.video.TpVideoSystem;
import com.android.rockchip.camera2.separated.MediaAdapter;
import com.android.rockchip.mediacodecnew.R;

import java.io.File;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * MediaBrowserActivity - integrated版本的媒体浏览器
 * <p>
 * 此Activity用于显示媒体文件的网格视图，并允许用户点击查看视频或图片。
 * 与separated版本不同，此版本使用TpVideoSystem进行视频播放，
 * 并在同一个Activity中处理图片查看和视频播放。
 * </p>
 */
public class MediaBrowserActivity extends AppCompatActivity {
    private static final String TAG = "MediaBrowserActivity";
    
    /* 媒体文件列表 */
    private List<File> mediaFiles = new ArrayList<>();
    
    /* 支持的图片格式 */
    private static final Set<String> SUPPORTED_IMAGE_FORMATS = new HashSet<>(
            Arrays.asList(".jpg", ".jpeg", ".png", ".bmp", ".gif", ".tiff", ".tif"));
    
    /* 支持的视频格式 */
    private static final Set<String> SUPPORTED_VIDEO_FORMATS = new HashSet<>(
            Arrays.asList(".mp4"));

    /* UI组件 */
    private RecyclerView recyclerView;

    /* 视频系统（仅用于初始化，播放由独立Activity处理） */
    private TpVideoSystem videoSystem;

    /**
     * Activity 的 onCreate 方法。
     * 初始化 RecyclerView 并加载媒体文件。
     *
     * @param savedInstanceState 保存的实例状态
     */
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.media_browser_integrated);

        // 初始化UI组件
        initViews();
        
        // 初始化视频系统
        initVideoSystem();
        
        // 加载媒体文件并设置适配器
        loadMediaFiles();
    }

    /**
     * 初始化UI组件
     */
    private void initViews() {
        /* 初始化 RecyclerView */
        recyclerView = findViewById(R.id.recycler_view);
        recyclerView.setLayoutManager(new GridLayoutManager(this, 3));
    }

    /**
     * 初始化视频系统
     */
    private void initVideoSystem() {
        // 创建TpVideoSystem实例（主要用于文件验证等功能）
        videoSystem = new TpVideoSystem(this);
        android.util.Log.d(TAG, "TpVideoSystem初始化完成（浏览器模式）");
    }

    /**
     * 加载媒体文件并设置适配器
     */
    private void loadMediaFiles() {
        // 清理图片缓存以提高性能
//        com.touptek.video.TpImageLoader.clearCache();
        
        /* 加载媒体文件 */
        mediaFiles = getMediaFiles();
        MediaAdapter adapter = new MediaAdapter(mediaFiles, this::onMediaClicked, this::onImageSelected);
        recyclerView.setAdapter(adapter);
        
        android.util.Log.d(TAG, "Loaded " + mediaFiles.size() + " media files");
    }

    /**
     * 获取媒体文件列表。
     *
     * @return 包含媒体文件的列表
     */
    private List<File> getMediaFiles() {
        /* 获取媒体文件的存储路径 */
        String mediaPath = TpFileManager.createVideoPath(this);
        File mediaDir = new File(mediaPath).getParentFile().getParentFile(); // 获取上级目录
        List<File> files = new ArrayList<>();
        searchMediaFiles(mediaDir, files);
        return files;
    }

    /**
     * 递归搜索指定目录中的媒体文件。
     *
     * @param dir   要搜索的目录
     * @param files 用于存储找到的媒体文件的列表
     */
    private void searchMediaFiles(File dir, List<File> files) {
        if (dir == null || !dir.isDirectory()) {
            return;
        }

        File[] fileList = dir.listFiles();
        if (fileList == null) return;

        for (File file : fileList) {
            if (file.isDirectory()) {
                searchMediaFiles(file, files);
            } else {
                String fileName = file.getName().toLowerCase();
                boolean isSupported = false;
                
                // 检查是否为支持的图片格式
                for (String format : SUPPORTED_IMAGE_FORMATS) {
                    if (fileName.endsWith(format)) {
                        isSupported = true;
                        break;
                    }
                }
                
                // 检查是否为支持的视频格式
                if (!isSupported) {
                    for (String format : SUPPORTED_VIDEO_FORMATS) {
                        if (fileName.endsWith(format)) {
                                isSupported = true;
                            break;
                        }
                    }
                }

                if (isSupported) {
                    files.add(file);
                }
            }
        }
    }

    /**
     * 当用户点击媒体文件时调用。
     * 根据文件类型进行相应处理。
     *
     * @param file 被点击的媒体文件
     */
    private void onMediaClicked(File file) {
        String fileName = file.getName().toLowerCase();
        
        // 检查是否为视频文件
        for (String format : SUPPORTED_VIDEO_FORMATS) {
            if (fileName.endsWith(format)) {
                playVideo(file);
                return;
            }
        }
        
        // 检查是否为图片文件
        for (String format : SUPPORTED_IMAGE_FORMATS) {
            if (fileName.endsWith(format)) {
                viewImage(file);
                return;
            }
        }
    }

    /**
     * 验证视频文件是否有效
     *
     * @param videoFile 要验证的视频文件
     * @return true如果文件有效，false如果无效
     */
    private boolean isValidVideoFile(File videoFile) {
        android.media.MediaMetadataRetriever retriever = null;
        try {
            retriever = new android.media.MediaMetadataRetriever();
            retriever.setDataSource(videoFile.getAbsolutePath());

            // 尝试获取视频时长
            String duration = retriever.extractMetadata(android.media.MediaMetadataRetriever.METADATA_KEY_DURATION);
            if (duration == null) {
                android.util.Log.w(TAG, "无法获取视频时长: " + videoFile.getName());
                return false;
            }

            // 尝试获取视频分辨率
            String width = retriever.extractMetadata(android.media.MediaMetadataRetriever.METADATA_KEY_VIDEO_WIDTH);
            String height = retriever.extractMetadata(android.media.MediaMetadataRetriever.METADATA_KEY_VIDEO_HEIGHT);

            android.util.Log.d(TAG, "视频信息 - 时长: " + duration + "ms, 分辨率: " + width + "x" + height);

            return true;
        } catch (Exception e) {
            android.util.Log.e(TAG, "验证视频文件失败: " + videoFile.getName(), e);
            return false;
        } finally {
            if (retriever != null) {
                try {
                    retriever.release();
                } catch (Exception e) {
                    android.util.Log.w(TAG, "释放MediaMetadataRetriever失败", e);
                }
            }
        }
    }

    /**
     * 播放视频文件
     *
     * @param videoFile 要播放的视频文件
     */
    private void playVideo(File videoFile) {
        android.util.Log.d(TAG, "启动独立播放器播放视频: " + videoFile.getAbsolutePath());

        // 检查文件是否存在和可读
        if (!videoFile.exists()) {
            Toast.makeText(this, "视频文件不存在: " + videoFile.getName(), Toast.LENGTH_LONG).show();
            return;
        }

        if (!videoFile.canRead()) {
            Toast.makeText(this, "无法读取视频文件: " + videoFile.getName(), Toast.LENGTH_LONG).show();
            return;
        }

        // 检查文件大小
        long fileSize = videoFile.length();
        android.util.Log.d(TAG, "视频文件大小: " + fileSize + " bytes");
        if (fileSize == 0) {
            Toast.makeText(this, "视频文件为空: " + videoFile.getName(), Toast.LENGTH_LONG).show();
            return;
        }

        // 验证视频文件有效性
        if (!isValidVideoFile(videoFile)) {
            Toast.makeText(this, "视频文件损坏或格式不支持: " + videoFile.getName(), Toast.LENGTH_LONG).show();
            return;
        }

        // 启动使用TpVideoPlayerView的播放Activity
        TpVideoPlayerActivity.start(this, videoFile.getAbsolutePath());
    }

    /**
     * 查看图片文件
     *
     * @param imageFile 要查看的图片文件
     */
    private void viewImage(File imageFile) {
        /* 启动图片查看器 Activity */
        Intent intent = new Intent(this, ImageViewerActivity.class);
        intent.putExtra("imagePath", imageFile.getAbsolutePath());
        startActivity(intent);
    }

    /**
     * 当图片被选择时调用，显示操作选择对话框
     *
     * @param imageFile 被选择的图片文件
     */
    private void onImageSelected(File imageFile) {
        showImageActionDialog(imageFile);
    }

    /**
     * 显示图片操作选择对话框
     *
     * @param imageFile 图片文件
     */
    private void showImageActionDialog(File imageFile) {
        new AlertDialog.Builder(this)
            .setTitle("选择操作")
            .setItems(new String[]{"查看图片", "图片视频对比"}, (dialog, which) -> {
                switch (which) {
                    case 0:
                        viewImage(imageFile);
                        break;
                    case 1:
                        startImageVideoCompare(imageFile);
                        break;
                }
            })
            .show();
    }

    /**
     * 启动图片视频对比功能
     *
     * @param imageFile 要对比的图片文件
     */
    private void startImageVideoCompare(File imageFile) {
        Intent intent = new Intent(this, ImageVideoCompareActivity.class);
        intent.putExtra("image_path", imageFile.getAbsolutePath());
        startActivity(intent);

        android.util.Log.d(TAG, "启动图片视频对比: " + imageFile.getName());
    }





    @Override
    public void onBackPressed() {
        android.util.Log.d(TAG, "用户按下返回键");
        super.onBackPressed();
    }

    @Override
    protected void onDestroy() {
        android.util.Log.d(TAG, "onDestroy - 清理资源");

        try {
            // 释放TpVideoSystem资源
            if (videoSystem != null) {
                videoSystem = null;
            }

            android.util.Log.d(TAG, "onDestroy - 资源清理完成");
        } catch (Exception e) {
            android.util.Log.e(TAG, "onDestroy时清理资源出错", e);
        } finally {
            super.onDestroy();
        }
    }
}
