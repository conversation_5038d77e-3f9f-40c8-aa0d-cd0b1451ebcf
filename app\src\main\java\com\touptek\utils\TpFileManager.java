package com.touptek.utils;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.os.Environment;
import android.os.StatFs;
import android.util.Log;

import java.io.BufferedReader;
import java.io.File;
import java.io.IOException;
import java.io.InputStreamReader;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Locale;

/**
 * TpFileManager 类提供文件存储相关的工具方法。
 * <p>
 * 此类提供了文件存储操作的各种实用方法，包括USB设备监控、媒体文件路径创建、
 * 存储空间检查和文件系统类型识别等功能。
 * </p>
 * 
 * <p><b>主要功能：</b></p>
 * <ul>
 *   <li>监控USB驱动器的插入和移除</li>
 *   <li>为视频和图片创建合适的存储路径</li>
 *   <li>获取存储设备的可用空间</li>
 *   <li>检测文件系统类型</li>
 *   <li>获取外部和内部存储路径</li>
 * </ul>
 * 
 * <p><b>使用示例：</b></p>
 * <pre>{@code
 * // 监听USB设备插拔
 * TpFileManager.startUsbDriveMonitor(context, new TpFileManager.StorageListener() {
 *     @Override
 *     public void onUsbDriveConnected(String rootPath) {
 *         Log.d(TAG, "USB驱动器已连接: " + rootPath);
 *         // 处理USB连接事件
 *     }
 *     
 *     @Override
 *     public void onUsbDriveDisconnected(String rootPath) {
 *         Log.d(TAG, "USB驱动器已断开: " + rootPath);
 *         // 处理USB断开事件
 *     }
 * });
 * 
 * // 创建视频和图片文件路径
 * String videoPath = TpFileManager.createVideoPath(context);
 * String imagePath = TpFileManager.createImagePath(context);
 * 
 * // 获取存储空间信息
 * long availableSpace = TpFileManager.getAvailableStorageSpace(videoPath);
 * String fsType = TpFileManager.getFileSystemType(videoPath);
 * }</pre>
 * 
 */
public class TpFileManager
{
    /** 日志标签 */
    private static final String TAG = "TpFileManager";

    /** 存储设备变化监听器 */
    private static StorageListener storageListener;
    
    /** 广播接收器 */
    private static BroadcastReceiver storageReceiver;
    
    /** USB连接状态跟踪变量 */
    private static boolean isUsbConnected = false;
    
    /** 当前连接的USB路径 */
    private static String currentUsbPath = null;
    
    /**
     * 存储设备变化监听接口。
     * <p>
     * 当U盘插入或拔出时，会通过此接口回调相应方法。
     * </p>
     */
    public interface StorageListener 
    {
        /**
         * 当U盘插入时调用。
         *
         * @param rootPath U盘根目录路径
         */
        void onUsbDriveConnected(String rootPath);
        
        /**
         * 当U盘拔出时调用。
         * 
         * @param rootPath 被拔出的U盘根目录路径
         */
        void onUsbDriveDisconnected(String rootPath);
    }
    
    /**
     * 开始监听U盘插拔事件。
     * <p>
     * 此方法会注册一个BroadcastReceiver来监听存储设备的变化。
     * 当检测到U盘插入或拔出时，会通过监听器回调相应方法。
     * 如果已有监听器在运行，会先停止之前的监听器，确保只有一个活动的监听实例。
     * 方法会立即检查当前状态，如果已有U盘插入则立即触发回调。
     * </p>
     *
     * @param context 应用上下文，用于注册广播接收器
     * @param listener 存储设备变化监听器，用于接收U盘插拔事件回调
     */
    public static void startUsbDriveMonitor(Context context, StorageListener listener) 
    {
        stopUsbDriveMonitor(context); // 确保先停止之前的监听器
        
        storageListener = listener;
        
        // 创建广播接收器
        storageReceiver = new BroadcastReceiver() 
        {
            @Override
            public void onReceive(Context context, Intent intent) 
            {
                String action = intent.getAction();
                Log.d(TAG, "Received storage device broadcast: " + action);
                
                if (Intent.ACTION_MEDIA_MOUNTED.equals(action)) 
                {
                    // U盘挂载
                    String mountPath = intent.getData().getPath();
                    Log.d(TAG, "USB drive mounted: " + mountPath);
                    
                    // 确认是否为可移动设备
                    if (isRemovableStorage(context, mountPath)) 
                    {
                        // 只有当状态发生变化或路径不同时才通知
                        if (!isUsbConnected || !mountPath.equals(currentUsbPath)) 
                        {
                            isUsbConnected = true;
                            currentUsbPath = mountPath;
                            
                            if (storageListener != null) 
                            {
                                storageListener.onUsbDriveConnected(mountPath);
                            }
                        }
                    }
                } 
                else if (Intent.ACTION_MEDIA_REMOVED.equals(action) || 
                          Intent.ACTION_MEDIA_UNMOUNTED.equals(action) ||
                          Intent.ACTION_MEDIA_BAD_REMOVAL.equals(action)) 
                {
                    // U盘移除或未正确卸载
                    String path = intent.getData().getPath();
                    Log.d(TAG, "USB drive removed: " + path);
                    
                    // 只有当U盘当前为连接状态才处理断开事件
                    if (isUsbConnected) 
                    {
                        String removedPath = currentUsbPath;
                        isUsbConnected = false;
                        currentUsbPath = null;
                        
                        if (storageListener != null && removedPath != null) 
                        {
                            storageListener.onUsbDriveDisconnected(removedPath);
                        }
                    }
                }
            }
        };
        
        // 注册广播接收器
        IntentFilter filter = new IntentFilter();
        filter.addAction(Intent.ACTION_MEDIA_MOUNTED);
        filter.addAction(Intent.ACTION_MEDIA_UNMOUNTED);
        filter.addAction(Intent.ACTION_MEDIA_REMOVED);
        filter.addAction(Intent.ACTION_MEDIA_BAD_REMOVAL);
        filter.addDataScheme("file");
        context.registerReceiver(storageReceiver, filter);
        
        // 立即检查当前状态，如果已有U盘插入则触发回调
        String usbPath = getExternalStoragePath(context);
        if (usbPath != null && storageListener != null) 
        {
            isUsbConnected = true;
            currentUsbPath = usbPath;
            storageListener.onUsbDriveConnected(usbPath);
        }

        Log.d(TAG, "USB drive monitor started");
    }
    
    /**
     * 停止监听U盘插拔事件。
     * <p>
     * 此方法会注销之前注册的BroadcastReceiver。
     * </p>
     *
     * @param context 应用上下文
     */
    public static void stopUsbDriveMonitor(Context context) 
    {
        if (storageReceiver != null) 
        {
            try 
            {
                context.unregisterReceiver(storageReceiver);
                Log.d(TAG, "USB drive monitor stopped");
            } 
            catch (IllegalArgumentException e) 
            {
                // 接收器可能尚未注册
                Log.e(TAG, "Failed to stop USB drive monitor: " + e.getMessage());
            }
            storageReceiver = null;
        }
        storageListener = null;
        isUsbConnected = false;
        currentUsbPath = null;
    }
    
    /**
     * 检查指定路径是否为可移动存储设备。
     *
     * @param context 应用上下文
     * @param path 存储路径
     * @return 如果是可移动存储设备返回true，否则返回false
     */
    private static boolean isRemovableStorage(Context context, String path) 
    {
        File[] externalFilesDirs = context.getExternalFilesDirs(null);
        for (File file : externalFilesDirs) 
        {
            if (file != null && file.getAbsolutePath().contains(path) && 
                Environment.isExternalStorageRemovable(file)) 
            {
                return true;
            }
        }
        return false;
    }

    /**
     * 创建带日期后缀的视频文件路径。
     * <p>
     * 此方法会根据当前日期和时间生成唯一的文件名，格式为"video_yyyyMMdd_HHmmss.mp4"。
     * 优先将文件存储在可拆卸的外部存储设备（如U盘）的DCIM/Videos目录中，
     * 如果未检测到U盘，则会使用设备内部存储的应用私有目录作为后备存储位置。
     * 此方法会自动创建必要的目录结构。
     * </p>
     *
     * @param context 应用上下文，用于访问外部存储目录
     * @return 生成的视频文件的绝对路径，可直接用于MediaRecorder或FileOutputStream
     */
    public static String createVideoPath(Context context)
    {
        /* 格式化当前日期和时间，生成文件名后缀 */
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyyMMdd_HHmmss", Locale.getDefault());
        String dateTimeSuffix = dateFormat.format(new Date());
        String fileName = "video_" + dateTimeSuffix + ".mp4";

        /* 获取所有外部存储目录 */
        File[] externalFilesDirs = context.getExternalFilesDirs(null);
        for (File file : externalFilesDirs)
        {
            /* 判断目录是否为可拆卸存储（如 U 盘）且已挂载 */
            if (file != null
                    && Environment.isExternalStorageRemovable(file)
                    && Environment.MEDIA_MOUNTED.equals(Environment.getExternalStorageState(file)))
            {
                /* 获取存储根目录 */
                String fullPath = file.getAbsolutePath();
                String basePath = fullPath.substring(0, fullPath.indexOf("/Android"));

                /* 创建 DCIM/Videos 目录 */
                File dir = new File(basePath, "DCIM/Videos");
                if (!dir.exists())
                {
                    dir.mkdirs();
                }
                Log.d(TAG, "USB drive detected: " + dir.getAbsolutePath());

                /* 打印日志 */
                Log.d(TAG, "Creating video output path");
                return new File(dir, fileName).getAbsolutePath();
            }
        }

        /* 如果没有检测到 U 盘，则使用默认主外部存储作为后备 */
        File primaryDir = context.getExternalFilesDir(null);
        File fallbackDir = new File(primaryDir, "Movies");
        if (!fallbackDir.exists())
        {
            fallbackDir.mkdirs();
        }
        if (primaryDir != null)
        {
            Log.d(TAG, "No USB drive detected, using primary external storage: " + primaryDir.getAbsolutePath());
        }

        /* 打印日志 */
        Log.d(TAG, "Creating video output path");
        return new File(fallbackDir, fileName).getAbsolutePath();
    }

    /**
     * 创建带日期后缀的图像文件路径。
     * <p>
     * 此方法会根据当前日期和时间生成唯一的文件名，格式为"image_yyyyMMdd_HHmmss.jpg"。
     * 优先将文件存储在可拆卸的外部存储设备（如U盘）的DCIM/Images目录中，
     * 如果没有检测到外部存储设备，则使用默认主外部存储中的Pictures目录。
     * </p>
     *
     * @param context 应用上下文
     * @return 返回完整的图像文件路径
     */
    public static String createImagePath(Context context)
    {
        // 默认使用JPEG格式
        return createImagePath(context, "image", true, "jpg");
    }
    
    /**
     * 创建自定义图像文件路径。
     * <p>
     * 此方法允许指定文件名前缀、是否包含日期时间以及文件格式。
     * 优先将文件存储在可拆卸的外部存储设备（如U盘）的DCIM/Images目录中，
     * 如果没有检测到外部存储设备，则使用默认主外部存储中的Pictures目录。
     * 内置智能文件名冲突检测，自动避免文件覆盖。
     * </p>
     *
     * @param context 应用上下文
     * @param prefix 文件名前缀，如"image"
     * @param includeDateTime 是否在文件名中包含日期时间
     * @param extension 文件扩展名，不包含点，如"jpg"、"png"或"bmp"
     * @return 返回唯一的图像文件路径，保证不会覆盖现有文件
     */
    public static String createImagePath(Context context, String prefix, boolean includeDateTime, String extension)
    {
        // 构建文件名
        StringBuilder fileNameBuilder = new StringBuilder();

        // 添加前缀
        fileNameBuilder.append(prefix);

        // 添加日期时间（如果需要）
        if (includeDateTime) {
            SimpleDateFormat dateFormat = new SimpleDateFormat("yyyyMMdd_HHmmss", Locale.getDefault());
            String dateTimeSuffix = dateFormat.format(new Date());
            fileNameBuilder.append("_").append(dateTimeSuffix);
        }

        // 添加扩展名
        fileNameBuilder.append(".").append(extension);

        String fileName = fileNameBuilder.toString();
        String initialPath = null;

        /* 获取所有外部存储目录 */
        File[] externalFilesDirs = context.getExternalFilesDirs(null);
        for (File file : externalFilesDirs)
        {
            /* 判断目录是否为可拆卸存储（如 U 盘）且已挂载 */
            if (file != null
                    && Environment.isExternalStorageRemovable(file)
                    && Environment.MEDIA_MOUNTED.equals(Environment.getExternalStorageState(file)))
            {
                /* 获取存储根目录 */
                String fullPath = file.getAbsolutePath();
                String basePath = fullPath.substring(0, fullPath.indexOf("/Android"));

                /* 创建 DCIM/Images 目录 */
                File dir = new File(basePath, "DCIM/Images");
                if (!dir.exists())
                {
                    dir.mkdirs();
                }
                Log.d(TAG, "USB drive detected: " + dir.getAbsolutePath());
                initialPath = new File(dir, fileName).getAbsolutePath();
                break;
            }
        }

        /* 如果没有检测到 U 盘，则使用默认主外部存储作为后备 */
        if (initialPath == null) {
            File primaryDir = context.getExternalFilesDir(null);
            File fallbackDir = new File(primaryDir, "Pictures");
            if (!fallbackDir.exists())
            {
                fallbackDir.mkdirs();
            }
            Log.d(TAG, "No USB drive detected, using primary external storage: " + primaryDir.getAbsolutePath());
            initialPath = new File(fallbackDir, fileName).getAbsolutePath();
        }

        // 使用智能文件命名功能，确保返回唯一路径
        String uniquePath = generateUniqueFileName(initialPath);

        // 如果路径发生了变化，记录日志
        if (!uniquePath.equals(initialPath)) {
            Log.d(TAG, "File name conflict resolved: " + initialPath + " -> " + uniquePath);
        }

        return uniquePath;
    }

    /**
     * 获取指定路径的可用存储空间。
     * <p>
     * 此方法会通过 StatFs 类获取指定路径的剩余存储空间大小。
     * 如果路径无效或发生异常，则返回 -1。
     * </p>
     *
     * @param path
     *        文件路径，用于检查其可用存储空间。
     * @return
     *        返回可用存储空间的大小（以字节为单位）。如果发生异常，则返回 -1。
     */
    public static long getAvailableStorageSpace(String path)
    {
        try
        {
            StatFs statFs = new StatFs(path);
            long availableBlocks = statFs.getAvailableBlocksLong();
            long blockSize = statFs.getBlockSizeLong();

            return availableBlocks * blockSize;
        }
        catch (Exception e)
        {
            Log.e(TAG, "Failed to get available storage space: " + e.getMessage());
            return -1;
        }
    }

    /**
     * 判断文件系统的格式。
     * <p>
     * 此方法通过执行系统命令 "mount" 来解析指定路径的文件系统类型。
     * 如果路径无效或发生异常，则返回 "Unknown"。
     * </p>
     *
     * @param path
     *        文件路径，用于检查其文件系统类型。
     * @return
     *        返回文件系统格式（如 FAT32（vfat）、exFAT、NTFS 等）。如果发生异常，则返回 "Unknown"。
     */
    public static String getFileSystemType(String path)
    {
        /* 将虚拟路径转换为实际路径 */
        String actualPath = convertToActualPath(path);
        try
        {
            String command = "mount";
            Process process = Runtime.getRuntime().exec(command);
            BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()));
            String line;
            while ((line = reader.readLine()) != null)
            {
                if (line.contains(actualPath))
                {
                    String[] parts = line.split("\\s+");
                    if (parts.length >= 5)
                    {
                        /* 打印日志 */
                        Log.d(TAG, "File system type detected");
                        return parts[4]; // 返回文件系统类型
                    }
                }
            }
            reader.close();
        }
        catch (IOException e)
        {
            Log.e(TAG, "Failed to get file system type: " + e.getMessage());
        }
        return "Unknown";
    }

    /**
     * 将虚拟路径转换为实际路径。
     * <p>
     * 此方法会将虚拟路径（如 "/storage/" 开头的路径）转换为实际路径（如 "/mnt/pass_through/0/" 开头的路径）。
     * 如果路径不需要转换，则直接返回原路径。
     * </p>
     *
     * @param virtualPath
     *        虚拟路径，通常以 "/storage/" 开头。
     * @return
     *        返回转换后的实际路径。如果路径不需要转换，则返回原路径。
     */
    private static String convertToActualPath(String virtualPath)
    {
        if (virtualPath.startsWith("/storage/"))
        {
            String partAfterStorage = virtualPath.substring("/storage/".length());
            String[] segments = partAfterStorage.split("/", 2);
            String targetPart = segments[0];

            /* 打印日志 */
            Log.d(TAG, "Converting virtual path to actual path");
            return "/mnt/pass_through/0/" + targetPart;
        }

        /* 打印日志 */
        Log.d(TAG, "Path does not need conversion");
        return virtualPath;
    }

    /**
     * 获取外部存储设备根目录。
     * <p>
     * 此方法会尝试获取可拆卸的外部存储设备（如 U 盘）的路径。
     * 如果未检测到外部存储设备，则返回 null。
     * </p>
     *
     * @param context
     *        应用上下文，用于访问外部存储目录。
     * @return
     *        返回外部存储设备的根路径。如果未检测到外部存储设备，则返回 null。
     */
    public static String getExternalStoragePath(Context context)
    {
        /* 获取所有外部存储目录 */
        File[] externalFilesDirs = context.getExternalFilesDirs(null);
        for (File file : externalFilesDirs)
        {
            /* 判断目录是否为可拆卸存储（如 U 盘）且已挂载 */
            if (file != null
                    && Environment.isExternalStorageRemovable(file)
                    && Environment.MEDIA_MOUNTED.equals(Environment.getExternalStorageState(file)))
            {
                /* 获取存储根目录 */
                String fullPath = file.getAbsolutePath();
                String basePath = fullPath.substring(0, fullPath.indexOf("/Android"));

                Log.d(TAG, "External storage device detected: " + basePath);
                return basePath;
            }
        }

        /* 如果未检测到外部存储设备，则返回 null */
        Log.e(TAG, "No external storage device detected");
        return null;
    }

    /**
     * 获取内部存储设备根目录。
     * <p>
     * 此方法会返回设备的内部存储路径根目录。
     * </p>
     *
     * @param context
     *        应用上下文，用于访问内部存储目录。
     * @return
     *        返回内部存储设备的根路径。如果无法获取，则返回 null。
     */
    public static String getInternalStoragePath(Context context)
    {
        /* 获取内部存储目录 */
        File primaryDir = context.getExternalFilesDir(null);
        if (primaryDir != null)
        {
            String fullPath = primaryDir.getAbsolutePath();
            String basePath = fullPath.substring(0, fullPath.indexOf("/Android"));

            Log.d(TAG, "Internal storage device path: " + basePath);
            return basePath;
        }

        /* 如果无法获取内部存储路径，返回 null */
        Log.e(TAG, "Unable to get internal storage path");
        return null;
    }

    /**
     * 生成唯一的文件名，避免文件覆盖。
     * <p>
     * 如果目标文件已存在，会自动添加递增后缀（如：image_1.jpg, image_2.jpg）。
     * 如果尝试100次仍有冲突，则使用时间戳确保唯一性。
     * 此方法是线程安全的，适用于并发场景。
     * </p>
     *
     * @param originalPath 原始文件路径
     * @return 唯一的文件路径，保证不会覆盖现有文件
     */
    public static String generateUniqueFileName(String originalPath)
    {
        File file = new File(originalPath);
        if (!file.exists())
        {
            Log.d(TAG, "File does not exist, using original name: " + originalPath);
            return originalPath; // 文件不存在，直接使用原名
        }

        // 解析文件名和扩展名
        String parent = file.getParent();
        String name = file.getName();
        String baseName, extension;

        int lastDot = name.lastIndexOf('.');
        if (lastDot > 0)
        {
            baseName = name.substring(0, lastDot);
            extension = name.substring(lastDot);
        }
        else
        {
            baseName = name;
            extension = "";
        }

        // 尝试生成唯一文件名，最多尝试100次
        for (int i = 1; i <= 100; i++)
        {
            String newName = baseName + "_" + i + extension;
            String newPath = parent != null ? new File(parent, newName).getAbsolutePath() : newName;
            if (!new File(newPath).exists())
            {
                Log.d(TAG, "Generated unique filename: " + originalPath + " -> " + newPath);
                return newPath;
            }
        }

        // 如果100次都重复，使用时间戳确保唯一性
        String timestamp = String.valueOf(System.currentTimeMillis());
        String uniqueName = baseName + "_" + timestamp + extension;
        String uniquePath = parent != null ? new File(parent, uniqueName).getAbsolutePath() : uniqueName;
        Log.d(TAG, "Using timestamp for unique filename: " + originalPath + " -> " + uniquePath);
        return uniquePath;
    }
}
