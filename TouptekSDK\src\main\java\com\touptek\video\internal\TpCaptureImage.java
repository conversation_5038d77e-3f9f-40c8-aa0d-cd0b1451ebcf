package com.touptek.video.internal;

import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.graphics.ImageFormat;
import android.graphics.Rect;
import android.graphics.YuvImage;
import android.media.Image;
import android.media.ImageReader;
import android.os.Handler;
import android.os.HandlerThread;
import android.util.Log;
import android.util.Size;

import java.io.ByteArrayOutputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.nio.ByteBuffer;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * TpCaptureImage 类用于处理摄像头图像的抓取和保存。
 * <p>
 * 此类提供了图像抓取、保存为 JPEG/PNG/BMP 文件以及资源释放的功能。
 * 它支持异步操作，避免阻塞主线程。
 * </p>
 * 
 * <p><b>主要功能：</b></p>
 * <ul>
 *   <li>高质量图像抓取</li>
 *   <li>支持多种图像格式 (JPEG/PNG/BMP)</li>
 *   <li>异步处理和保存</li>
 *   <li>自动缩放适配</li>
 *   <li>丰富的回调接口</li>
 * </ul>
 * 
 * <p><b>使用示例：</b></p>
 * <pre>{@code
 * // 创建抓图助手
 * TpCaptureImage captureHelper = TpCaptureImage.builder(new Size(3840, 2160))
 *     .onImageSaved(filePath -> {
 *         // 图像保存成功的处理
 *         Log.d(TAG, "Image saved to: " + filePath);
 *     })
 *     .onError(errorMessage -> {
 *         // 错误处理
 *         Log.e(TAG, "Error: " + errorMessage);
 *     })
 *     .setImageOutputFormat(TpCaptureImage.FORMAT_JPEG)
 *     .build();
 *     
 * // 请求抓图
 * String outputPath = "/storage/emulated/0/Pictures/capture.jpg";
 * captureHelper.requestCapture(new Size(1920, 1080), outputPath);
 * 
 * // 使用完毕后释放资源
 * captureHelper.release();
 * }</pre>
 * 
 * <p><strong>注意：</strong> 使用Builder模式创建实例，确保在不再需要时调用{@link #release()}方法释放资源。</p>
 */
public class TpCaptureImage
{
    private static final String TAG = "TpCaptureImage";
    
    // 图像格式常量
    /**
     * JPEG格式常量
     * <p>
     * 使用JPEG压缩算法，支持有损压缩，文件较小，适合照片。
     * 文件扩展名通常为.jpg或.jpeg
     * </p>
     */
    public static final int FORMAT_JPEG = 0;
    
    /**
     * PNG格式常量
     * <p>
     * 使用PNG压缩算法，支持无损压缩和透明通道，文件较大，适合需要保留细节的图像。
     * 文件扩展名通常为.png
     * </p>
     */
    public static final int FORMAT_PNG = 1;
    
    /**
     * BMP格式常量
     * <p>
     * 使用BMP位图格式，几乎无压缩，文件最大，适合需要原始数据的场景。
     * 文件扩展名通常为.bmp
     * </p>
     */
    public static final int FORMAT_BMP = 2;

    /**
     * TIFF格式常量
     * <p>
     * 使用TIFF格式，支持高质量无损压缩，适合科学图像和需要保留细节的场景。
     * 文件扩展名通常为.tiff或.tif
     * </p>
     */
    public static final int FORMAT_TIFF = 3;

    /* ImageReader 用于接收摄像头输出的图像数据 */
    private final ImageReader imageReader;

    /* 后台线程的 Handler，用于处理耗时操作 */
    private final Handler backgroundHandler;

    /* 后台线程，用于避免阻塞主线程 */
    private final HandlerThread backgroundThread;

    /* 回调接口，用于通知抓图结果 */
    private ImageCaptureListener imageCaptureListener;

    /* 标志位，表示是否有抓图请求 */
    private volatile boolean isCaptureRequested = false;

    /* 请求抓图的目标尺寸 */
    private Size requestedSize;

    /* 抓图保存的输出路径 */
    private String outputPath;
    
    /* 图像保存格式 */
    private int imageOutputFormat = FORMAT_JPEG;

    /* 并发控制：当前正在处理的图像数量 */
    private final AtomicInteger currentProcessingCount = new AtomicInteger(0);

    /* 最大并发处理数量，防止内存溢出 */
    private static final int MAX_CONCURRENT_PROCESSING = 3;

    /**
     * 回调接口，用于通知抓图结果
     * <p>
     * 此接口定义了图像保存成功和失败时的回调方法。
     * 实现此接口可以自定义处理抓图结果。
     * </p>
     */
    public interface ImageCaptureListener
    {
        /**
         * 当图像保存成功时调用
         * <p>
         * 在图像成功保存到指定路径后调用此方法。
         * 此方法在后台线程中执行，如需更新UI，请切换到主线程。
         * </p>
         * 
         * @param filePath 图像保存的完整文件路径
         */
        void onImageSaved(String filePath);

        /**
         * 当抓图失败时调用
         * <p>
         * 在抓图过程中发生错误时调用此方法。
         * 此方法在后台线程中执行，如需更新UI，请切换到主线程。
         * </p>
         * 
         * @param errorMessage 错误信息描述
         */
        void onError(String errorMessage);
    }

    /**
     * 私有构造函数，通过Builder模式创建实例
     * 
     * @param builder 构建器实例
     */
    private TpCaptureImage(Builder builder) {
        this.requestedSize = builder.imageSize;
        this.imageCaptureListener = builder.imageCaptureListener;
        this.imageReader = ImageReader.newInstance(
            builder.imageSize.getWidth(),
            builder.imageSize.getHeight(),
            builder.imageFormat,
            builder.maxImages
        );
        this.imageReader.setOnImageAvailableListener(this::onImageAvailable, builder.backgroundHandler);
        this.backgroundHandler = builder.backgroundHandler;
        this.backgroundThread = builder.backgroundThread;
        this.imageOutputFormat = builder.imageOutputFormat;
    }

    /**
     * 创建Builder实例
     * <p>
     * 使用Builder模式创建CaptureImageHelper实例，支持链式调用设置参数。
     * </p>
     * 
     * @param imageSize 图像尺寸，指定ImageReader的输出分辨率
     * @return 新的Builder实例
     * @throws IllegalArgumentException 如果imageSize为null
     */
    public static Builder builder(Size imageSize) {
        return new Builder(imageSize);
    }

    /**
     * CaptureImageHelper构建器类
     * <p>
     * 使用Builder模式构建CaptureImageHelper实例，提供流畅的链式调用API。
     * </p>
     */
    public static class Builder {
        private final Size imageSize;
        private ImageCaptureListener imageCaptureListener;
        private int imageFormat = ImageFormat.YUV_420_888;
        private int maxImages = 50;
        private Handler backgroundHandler;
        private HandlerThread backgroundThread;
        private int imageOutputFormat = FORMAT_JPEG; // 默认JPEG格式
        
        // 单独的回调处理器
        private java.util.function.Consumer<String> onImageSavedHandler;
        private java.util.function.Consumer<String> onErrorHandler;

        /**
         * 构造函数
         * <p>
         * 创建Builder实例，必须指定图像尺寸。
         * 会自动创建后台线程和Handler用于异步处理。
         * </p>
         * 
         * @param imageSize 图像尺寸，例如new Size(3840, 2160)表示4K分辨率
         * @throws IllegalArgumentException 如果imageSize为null
         */
        private Builder(Size imageSize) {
            if (imageSize == null) {
                throw new IllegalArgumentException("图像尺寸不能为null");
            }
            this.imageSize = imageSize;
            this.backgroundThread = new HandlerThread("CaptureImageBackground");
            this.backgroundThread.start();
            this.backgroundHandler = new Handler(backgroundThread.getLooper());
        }

        /**
         * 设置抓图回调
         * <p>
         * 设置完整的CaptureCallback接口实现，用于接收抓图结果通知。
         * 如果同时设置了onImageSaved和onError回调，则此设置优先。
         * </p>
         * 
         * @param imageCaptureListener 回调接口实现
         * @return Builder实例，用于链式调用
         */
        public Builder setCaptureCallback(ImageCaptureListener imageCaptureListener) {
            this.imageCaptureListener = imageCaptureListener;
            return this;
        }
        
        /**
         * 设置图像保存成功的回调
         * <p>
         * 设置图像保存成功时的回调处理器，简化回调设置。
         * 此方法是setCaptureCallback的简化版本，只关注成功事件。
         * </p>
         * 
         * @param handler 处理保存路径的回调函数
         * @return Builder实例，用于链式调用
         */
        public Builder onImageSaved(java.util.function.Consumer<String> handler) {
            this.onImageSavedHandler = handler;
            return this;
        }
        
        /**
         * 设置图像保存失败的回调
         * <p>
         * 设置图像保存失败时的回调处理器，简化回调设置。
         * 此方法是setCaptureCallback的简化版本，只关注错误事件。
         * </p>
         * 
         * @param handler 处理错误信息的回调函数
         * @return Builder实例，用于链式调用
         */
        public Builder onError(java.util.function.Consumer<String> handler) {
            this.onErrorHandler = handler;
            return this;
        }

        /**
         * 设置图像格式
         * <p>
         * 设置ImageReader的图像格式，默认为ImageFormat.YUV_420_888。
         * 注意：更改此设置可能会影响与某些相机设备的兼容性。
         * </p>
         * 
         * @param imageFormat 图像格式，来自ImageFormat类的常量
         * @return Builder实例，用于链式调用
         */
        public Builder setImageFormat(int imageFormat) {
            this.imageFormat = imageFormat;
            return this;
        }

        /**
         * 设置最大图像数量
         * <p>
         * 设置ImageReader可以同时持有的最大图像数量。
         * 较大的值可以缓冲更多图像，但会消耗更多内存。
         * </p>
         * 
         * @param maxImages 最大图像数量，默认值为50
         * @return Builder实例，用于链式调用
         */
        public Builder setMaxImages(int maxImages) {
            this.maxImages = maxImages;
            return this;
        }
        
        /**
         * 设置图像输出格式
         * <p>
         * 设置保存图像的格式。可选值：
         * <ul>
         *   <li>{@link #FORMAT_JPEG} - JPEG格式，有损压缩，适合照片 (默认)</li>
         *   <li>{@link #FORMAT_PNG} - PNG格式，无损压缩，适合需要保留细节的图像</li>
         *   <li>{@link #FORMAT_BMP} - BMP格式，几乎无压缩，适合需要原始数据的场景</li>
         * </ul>
         * 
         * @param format 输出格式常量
         * @return Builder实例，用于链式调用
         */
        public Builder setImageOutputFormat(int format) {
            this.imageOutputFormat = format;
            return this;
        }

        /**
         * 构建CaptureImageHelper实例
         * <p>
         * 根据之前设置的参数创建一个新的CaptureImageHelper实例。
         * 如果设置了单独的回调处理器但没有设置完整的CaptureCallback，
         * 会自动创建一个CaptureCallback实现。
         * </p>
         * 
         * @return 新创建的CaptureImageHelper实例
         */
        public TpCaptureImage build() {
            // 如果设置了单独的回调处理器，但没有设置完整的CaptureCallback
            if (imageCaptureListener == null && (onImageSavedHandler != null || onErrorHandler != null)) {
                imageCaptureListener = new ImageCaptureListener() {
                    @Override
                    public void onImageSaved(String filePath) {
                        if (onImageSavedHandler != null) {
                            onImageSavedHandler.accept(filePath);
                        }
                    }
                    
                    @Override
                    public void onError(String errorMessage) {
                        if (onErrorHandler != null) {
                            onErrorHandler.accept(errorMessage);
                        }
                    }
                };
            }
            
            return new TpCaptureImage(this);
        }
    }

    /**
     * 获取 ImageReader 实例。
     * <p>
     * 返回用于接收摄像头图像数据的ImageReader实例。
     * 通常将此Surface作为摄像头输出目标之一。
     * </p>
     *
     * @return ImageReader实例，可用于获取Surface
     * @see android.hardware.camera2.CameraDevice#createCaptureSession
     */
    public ImageReader getImageReader()
    {
        return imageReader;
    }

    /**
     * 设置抓图回调。
     * <p>
     * 设置一个回调接口，当图像保存成功或失败时会触发回调。
     * 可以在运行时更改回调实现。
     * </p>
     *
     * @param callback 回调接口实现，如果为null则取消回调
     */
    public void setCaptureCallback(ImageCaptureListener callback)
    {
        this.imageCaptureListener = callback;
    }


    /**
     * 请求抓图。
     * <p>
     * 此方法会设置抓图的目标尺寸和保存路径，并触发抓图操作。
     * 会自动根据文件后缀名确定保存格式。
     * </p>
     *
     * @param size 抓图的目标尺寸，如果与相机原始尺寸不同，会自动缩放
     * @param outputPath 抓图保存的输出路径，应包含完整的文件名和扩展名
     */
    public void requestCapture(Size size, String outputPath)
    {
        // 根据文件后缀名自动确定格式
        int format = detectFormatFromPath(outputPath);
        
        // 调用带格式参数的方法
        requestCapture(size, outputPath, format);
    }
    
    /**
     * 根据文件路径检测图像格式
     * 
     * @param path 文件路径
     * @return 检测到的图像格式常量
     */
    private int detectFormatFromPath(String path) {
        if (path == null || path.isEmpty()) {
            return FORMAT_JPEG; // 默认为JPEG
        }
        
        String lowercasePath = path.toLowerCase();
        
        if (lowercasePath.endsWith(".png")) {
            return FORMAT_PNG;
        } else if (lowercasePath.endsWith(".bmp")) {
            return FORMAT_BMP;
        } else if (lowercasePath.endsWith(".tiff") || lowercasePath.endsWith(".tif")) {
            return FORMAT_TIFF;
        } else {
            // .jpg, .jpeg 或其他格式默认使用JPEG
            return FORMAT_JPEG;
        }
    }

    /**
     * 请求抓图并指定输出格式。
     * <p>
     * 此方法会设置抓图的目标尺寸、保存路径和输出格式，并触发抓图操作。
     * 无论文件扩展名是什么，都会使用指定的格式保存。
     * 如果当前正在处理的图像数量已达到最大限制，将通过onError回调通知用户。
     * </p>
     *
     * @param size 抓图的目标尺寸，如果与相机原始尺寸不同，会自动缩放
     * @param outputPath 抓图保存的输出路径，应包含完整的文件名
     * @param format 图像输出格式 ({@link #FORMAT_JPEG}, {@link #FORMAT_PNG}, {@link #FORMAT_BMP})
     */
    public void requestCapture(Size size, String outputPath, int format)
    {
        // 检查当前并发处理数量
        int currentCount = currentProcessingCount.get();
        if (currentCount >= MAX_CONCURRENT_PROCESSING) {
            // 达到最大并发限制，拒绝请求并通知用户
            Log.w(TAG, "Capture request rejected: too many concurrent operations (" + currentCount + "/" + MAX_CONCURRENT_PROCESSING + ")");
            if (imageCaptureListener != null) {
                imageCaptureListener.onError("图像处理繁忙，请稍后再试 (" + currentCount + "/" + MAX_CONCURRENT_PROCESSING + " 正在处理中)");
            }
            return;
        }

        this.imageOutputFormat = format;
        this.isCaptureRequested = true;
        this.requestedSize = size;
        this.outputPath = outputPath;
    }

    /**
     * 当 ImageReader 有新的图像数据时调用。
     * <p>
     * 此方法会处理新的图像数据，并根据抓图请求将图像保存为指定格式文件。
     * 如果没有抓图请求，则直接释放图像。
     * 使用并发控制机制防止内存溢出。
     * </p>
     *
     * @param reader
     *        ImageReader 实例。
     */
    private void onImageAvailable(ImageReader reader)
    {
        /* 获取最新的图像 */
        Image image = reader.acquireLatestImage();
        if (image == null) return;

        if (isCaptureRequested)
        {
            isCaptureRequested = false; // 重置抓图请求标志

            // 增加并发计数器
            int currentCount = currentProcessingCount.incrementAndGet();
            Log.d(TAG, "Starting image processing, concurrent count: " + currentCount + "/" + MAX_CONCURRENT_PROCESSING);

            /* 在主线程复制图像数据并立即释放图像 */
            Size cameraSize = new Size(image.getWidth(), image.getHeight());
            ByteBuffer yuvData = copyImageToBuffer(image);
            image.close(); // 立即释放图像

            /* 在后台线程处理图像数据 */
            backgroundHandler.post(() ->
            {
                try
                {
                    /* 将 YUV 数据转换为 Bitmap */
                    Bitmap bitmap = convertYUVToBitmap(yuvData, cameraSize, requestedSize);

                    /* 根据设置的格式保存图像，传入包装的回调来处理计数器 */
                    saveImageWithFormat(bitmap, outputPath, imageOutputFormat);

                }
                catch (Exception e)
                {
                    Log.e(TAG, "Error processing image", e);

                    /* 通知回调图像保存失败并减少计数器 */
                    if (imageCaptureListener != null)
                    {
                        imageCaptureListener.onError("图像处理失败: " + e.getMessage());
                    }

                    // 处理失败时减少并发计数器
                    int remainingCount = currentProcessingCount.decrementAndGet();
                    Log.d(TAG, "Image processing failed, remaining concurrent count: " + remainingCount);
                }
            });
        }
        else
        {
            /* 如果没有抓图请求，立即释放图像 */
            image.close();
        }
    }

    /**
     * 将 YUV 数据转换为 Bitmap。
     * <p>
     * 此方法会将摄像头输出的 YUV 数据转换为 Bitmap，并支持缩放到目标尺寸。
     * </p>
     *
     * @param yuvData
     *        包含 YUV 数据的 ByteBuffer。
     * @param cameraSize
     *        摄像头的原始图像尺寸。
     * @param targetSize
     *        目标图像尺寸。
     * @return
     *        返回处理后的Bitmap。
     */
    private Bitmap convertYUVToBitmap(ByteBuffer yuvData, Size cameraSize, Size targetSize)
    {
        /* 将 YUV 数据转换为 NV21 格式 */
        byte[] nv21 = convertYUV420ToNV21(yuvData, cameraSize.getWidth(), cameraSize.getHeight());

        /* 使用 YuvImage 将 NV21 数据转换为 JPEG */
        YuvImage yuvImage = new YuvImage(nv21, ImageFormat.NV21, cameraSize.getWidth(), cameraSize.getHeight(), null);
        ByteArrayOutputStream out = new ByteArrayOutputStream();
        yuvImage.compressToJpeg(new Rect(0, 0, cameraSize.getWidth(), cameraSize.getHeight()), 100, out);

        /* 将 JPEG 数据解码为 Bitmap */
        Bitmap originalBitmap = BitmapFactory.decodeByteArray(out.toByteArray(), 0, out.size());

        /* 如果目标尺寸与原始尺寸相同，直接返回原始Bitmap */
        if (targetSize.equals(cameraSize))
        {
            return originalBitmap;
        }
        else
        {
            /* 缩放 Bitmap 到目标尺寸 */
            return Bitmap.createScaledBitmap(originalBitmap, targetSize.getWidth(), targetSize.getHeight(), true);
        }
    }
    


    /**
     * 将 Image 数据复制到缓冲区。
     * <p>
     * 此方法会将 Image 对象中的 YUV 数据复制到一个 ByteBuffer 中。
     * </p>
     *
     * @param image
     *        YUV_420_888 格式的 Image。
     * @return
     *        返回包含 YUV 数据的 ByteBuffer。
     */
    private ByteBuffer copyImageToBuffer(Image image)
    {
        int bufferSize = 0;
        for (Image.Plane plane : image.getPlanes())
        {
            bufferSize += plane.getBuffer().remaining();
        }

        ByteBuffer buffer = ByteBuffer.allocateDirect(bufferSize);
        for (Image.Plane plane : image.getPlanes())
        {
            ByteBuffer planeBuffer = plane.getBuffer();
            byte[] planeData = new byte[planeBuffer.remaining()];
            planeBuffer.get(planeData);
            buffer.put(planeData);
        }
        buffer.rewind();
        return buffer;
    }

    /**
     * 将 YUV_420_888 格式转换为 NV21 格式。
     * <p>
     * 此方法会将 YUV 数据转换为 NV21 格式，以便使用 YuvImage 进行处理。
     * </p>
     *
     * @param yuvData
     *        包含 YUV 数据的 ByteBuffer。
     * @param width
     *        图像宽度。
     * @param height
     *        图像高度。
     * @return
     *        返回 NV21 格式的字节数组。
     */
    private byte[] convertYUV420ToNV21(ByteBuffer yuvData, int width, int height)
    {
        int frameSize = width * height;
        int chromaSize = frameSize / 4;
        byte[] nv21 = new byte[frameSize + frameSize / 2];

        byte[] yPlane = new byte[frameSize];
        byte[] uvPlane = new byte[2 * chromaSize];

        yuvData.position(0);
        yuvData.get(yPlane, 0, frameSize);
        yuvData.get(uvPlane, 0, 2 * chromaSize);

        System.arraycopy(yPlane, 0, nv21, 0, frameSize);
        for (int i = 0; i < chromaSize; i++)
        {
            nv21[frameSize + i * 2] = uvPlane[i * 2 + 1];
            nv21[frameSize + i * 2 + 1] = uvPlane[i * 2];
        }

        return nv21;
    }

    /**
     * 保存Bitmap为BMP格式文件。
     * <p>
     * 由于Android没有直接支持BMP格式的压缩，此方法实现了BMP文件格式的写入。
     * 使用缓冲区批量写入提高性能。
     * </p>
     *
     * @param bitmap
     *        要保存的Bitmap对象。
     * @param outputPath
     *        输出文件路径。
     * @throws IOException
     *         如果保存失败，则抛出异常。
     */
    private void saveBitmap(Bitmap bitmap, String outputPath) throws IOException {
        int width = bitmap.getWidth();
        int height = bitmap.getHeight();
        int[] pixels = new int[width * height];
        bitmap.getPixels(pixels, 0, width, 0, 0, width, height);
        
        // 计算文件大小
        int fileSize = 54 + 4 * width * height; // 文件头(14) + DIB头(40) + 像素数据(4*width*height)
        
        try (FileOutputStream output = new FileOutputStream(outputPath)) {
            // 准备文件头 (14 bytes)
            byte[] bmpHeader = new byte[14];
            bmpHeader[0] = 'B';
            bmpHeader[1] = 'M';
            System.arraycopy(intToBytes(fileSize), 0, bmpHeader, 2, 4); // 文件大小
            System.arraycopy(intToBytes(0), 0, bmpHeader, 6, 4); // 保留字段
            System.arraycopy(intToBytes(54), 0, bmpHeader, 10, 4); // 数据偏移量
            
            // 准备DIB头 (40 bytes)
            byte[] dibHeader = new byte[40];
            System.arraycopy(intToBytes(40), 0, dibHeader, 0, 4); // DIB头大小
            System.arraycopy(intToBytes(width), 0, dibHeader, 4, 4); // 宽度
            System.arraycopy(intToBytes(height), 0, dibHeader, 8, 4); // 高度
            dibHeader[12] = 1; // 色彩平面数
            dibHeader[14] = 32; // 每像素位数
            System.arraycopy(intToBytes(0), 0, dibHeader, 16, 4); // 压缩方式
            System.arraycopy(intToBytes(4 * width * height), 0, dibHeader, 20, 4); // 图像数据大小
            // 其余参数保持为0
            
            // 写入文件头和DIB头
            output.write(bmpHeader);
            output.write(dibHeader);
            
            // 为像素数据创建缓冲区
            byte[] pixelBuffer = new byte[width * 4]; // 每行的缓冲区
            
            // 从下到上写入像素数据
            for (int y = height - 1; y >= 0; y--) {
                // 填充一行的缓冲区
                for (int x = 0; x < width; x++) {
                    int pixel = pixels[y * width + x];
                    int offset = x * 4;
                    pixelBuffer[offset] = (byte) (pixel & 0xFF); // Blue
                    pixelBuffer[offset + 1] = (byte) ((pixel >> 8) & 0xFF); // Green
                    pixelBuffer[offset + 2] = (byte) ((pixel >> 16) & 0xFF); // Red
                    pixelBuffer[offset + 3] = (byte) ((pixel >> 24) & 0xFF); // Alpha
                }
                // 一次性写入整行
                output.write(pixelBuffer);
            }
        }
    }
    
    /**
     * 保存Bitmap为TIFF格式文件。
     * <p>
     * 实现了基本的TIFF文件格式写入，支持无压缩的RGB数据。
     * </p>
     *
     * @param bitmap
     *        要保存的Bitmap对象。
     * @param outputPath
     *        输出文件路径。
     * @throws IOException
     *         如果保存失败，则抛出异常。
     */
    private void saveTiff(Bitmap bitmap, String outputPath) throws IOException {
        int width = bitmap.getWidth();
        int height = bitmap.getHeight();
        int[] pixels = new int[width * height];
        bitmap.getPixels(pixels, 0, width, 0, 0, width, height);
        
        try (FileOutputStream output = new FileOutputStream(outputPath)) {
            // TIFF头部 (8字节)
            byte[] header = new byte[8];
            // "II"表示小端序 (Intel格式)
            header[0] = 'I';
            header[1] = 'I';
            // 版本号 (42)
            header[2] = 42;
            header[3] = 0;
            // 第一个IFD的偏移量
            header[4] = 8;
            header[5] = 0;
            header[6] = 0;
            header[7] = 0;
            output.write(header);
            
            // 计算偏移量
            int ifdOffset = 8;
            int stripOffset = ifdOffset + 150; // IFD的大致大小
            int dataSize = width * height * 3; // RGB数据 (每像素3字节)
            
            // 写入IFD (图像文件目录)
            // 目录条目数量
            output.write(new byte[]{12, 0}); // 12个条目
            
            // ImageWidth标签
            writeTag(output, 256, 4, 1, width);
            
            // ImageLength标签
            writeTag(output, 257, 4, 1, height);
            
            // BitsPerSample标签
            int bitsPerSampleOffset = stripOffset + dataSize;
            writeTag(output, 258, 3, 3, bitsPerSampleOffset);
            
            // Compression标签 (1 = 无压缩)
            writeTag(output, 259, 3, 1, 1);
            
            // PhotometricInterpretation标签 (2 = RGB)
            writeTag(output, 262, 3, 1, 2);
            
            // StripOffsets标签
            writeTag(output, 273, 4, 1, stripOffset);
            
            // SamplesPerPixel标签 (3表示RGB)
            writeTag(output, 277, 3, 1, 3);
            
            // RowsPerStrip标签
            writeTag(output, 278, 4, 1, height);
            
            // StripByteCounts标签
            writeTag(output, 279, 4, 1, dataSize);
            
            // XResolution标签
            int xResOffset = bitsPerSampleOffset + 6;
            writeTag(output, 282, 5, 1, xResOffset);
            
            // YResolution标签
            int yResOffset = xResOffset + 8;
            writeTag(output, 283, 5, 1, yResOffset);
            
            // ResolutionUnit标签 (2 = 英寸)
            writeTag(output, 296, 3, 1, 2);
            
            // 下一个IFD偏移量 (0 = 没有更多IFD)
            output.write(new byte[]{0, 0, 0, 0});
            
            // 写入像素数据
            byte[] pixelData = new byte[width * height * 3];
            for (int i = 0; i < pixels.length; i++) {
                int pixel = pixels[i];
                int offset = i * 3;
                pixelData[offset] = (byte) ((pixel >> 16) & 0xFF);  // 红
                pixelData[offset + 1] = (byte) ((pixel >> 8) & 0xFF);  // 绿
                pixelData[offset + 2] = (byte) (pixel & 0xFF);  // 蓝
            }
            output.write(pixelData);
            
            // 写入BitsPerSample值
            output.write(new byte[]{8, 0, 8, 0, 8, 0});  // 红绿蓝各8位
            
            // 写入XResolution (72/1)
            output.write(new byte[]{72, 0, 0, 0, 1, 0, 0, 0});
            
            // 写入YResolution (72/1)
            output.write(new byte[]{72, 0, 0, 0, 1, 0, 0, 0});
        }
    }
    
    /**
     * 写入TIFF标签的辅助方法
     */
    private void writeTag(FileOutputStream output, int tagId, int dataType, int count, int value) throws IOException {
        // 标签ID (2字节)
        output.write((tagId & 0xFF));
        output.write(((tagId >> 8) & 0xFF));
        
        // 数据类型 (2字节)
        output.write((dataType & 0xFF));
        output.write(((dataType >> 8) & 0xFF));
        
        // 计数 (4字节)
        output.write((count & 0xFF));
        output.write(((count >> 8) & 0xFF));
        output.write(((count >> 16) & 0xFF));
        output.write(((count >> 24) & 0xFF));
        
        // 值/偏移量 (4字节)
        output.write((value & 0xFF));
        output.write(((value >> 8) & 0xFF));
        output.write(((value >> 16) & 0xFF));
        output.write(((value >> 24) & 0xFF));
    }

    /**
     * 异步保存图片，避免阻塞主线程或相机处理线程。
     *
     * @param bitmap 要保存的Bitmap对象
     * @param outputPath 输出文件路径
     * @param format 输出格式 (FORMAT_JPEG, FORMAT_PNG, FORMAT_BMP)
     * @param callback 保存完成后的回调
     */
    private void saveImageAsync(Bitmap bitmap, String outputPath, int format, ImageCaptureListener callback) {
        // 创建专用的线程进行图片保存，避免阻塞相机处理线程
        new Thread(() -> {
            try {
                Bitmap.CompressFormat compressFormat;
                switch (format) {
                    case FORMAT_PNG:
                        compressFormat = Bitmap.CompressFormat.PNG;
                        break;
                    case FORMAT_BMP:
                        saveBitmap(bitmap, outputPath);
                        if (callback != null) {
                            backgroundHandler.post(() -> callback.onImageSaved(outputPath));
                        }
                        return;
                    case FORMAT_TIFF:  // 添加TIFF处理
                        saveTiff(bitmap, outputPath);
                        if (callback != null) {
                            backgroundHandler.post(() -> callback.onImageSaved(outputPath));
                        }
                        return;
                    case FORMAT_JPEG:
                    default:
                        compressFormat = Bitmap.CompressFormat.JPEG;
                        break;
                }
                
                try (FileOutputStream output = new FileOutputStream(outputPath)) {
                    bitmap.compress(compressFormat, 100, output);
                }
                
                // 在原始后台线程中执行回调，保持一致性
                if (callback != null) {
                    backgroundHandler.post(() -> callback.onImageSaved(outputPath));
                }
            } catch (IOException e) {
                Log.e(TAG, "Error saving image", e);
                if (callback != null) {
                    backgroundHandler.post(() -> callback.onError("Failed to save image"));
                }
            }
        }).start();
    }

    /**
     * 根据指定格式保存图像到文件。
     * <p>
     * 此方法会将Bitmap保存为指定格式的文件。
     * 所有格式均使用异步方式保存以避免阻塞相机处理线程。
     * </p>
     *
     * @param bitmap
     *        要保存的Bitmap对象。
     * @param outputPath
     *        输出文件路径。
     * @param format
     *        输出格式 (FORMAT_JPEG, FORMAT_PNG, FORMAT_BMP)
     * @throws IOException
     *         如果保存失败，则抛出异常。
     */
    private void saveImageWithFormat(Bitmap bitmap, String outputPath, int format) throws IOException
    {
        // 所有格式都使用异步保存
        Log.d(TAG, "Using async save for image: " + outputPath + ", format: " +
              (format == FORMAT_PNG ? "PNG" : format == FORMAT_BMP ? "BMP" : "JPEG"));

        // 创建包装的回调，在保存完成后减少并发计数器
        ImageCaptureListener wrappedCallback = new ImageCaptureListener() {
            @Override
            public void onImageSaved(String filePath) {
                // 保存成功后减少并发计数器
                int remainingCount = currentProcessingCount.decrementAndGet();
                Log.d(TAG, "Image saved successfully, remaining concurrent count: " + remainingCount);

                // 调用原始回调
                if (imageCaptureListener != null) {
                    imageCaptureListener.onImageSaved(filePath);
                }
            }

            @Override
            public void onError(String errorMessage) {
                // 保存失败后减少并发计数器
                int remainingCount = currentProcessingCount.decrementAndGet();
                Log.d(TAG, "Image save failed, remaining concurrent count: " + remainingCount);

                // 调用原始回调
                if (imageCaptureListener != null) {
                    imageCaptureListener.onError(errorMessage);
                }
            }
        };

        saveImageAsync(bitmap, outputPath, format, wrappedCallback);
    }

    /**
     * 将int转换为小端字节数组。
     */
    private byte[] intToBytes(int value) {
        return new byte[] {
            (byte) (value & 0xFF),
            (byte) ((value >> 8) & 0xFF),
            (byte) ((value >> 16) & 0xFF),
            (byte) ((value >> 24) & 0xFF)
        };
    }

    /**
     * 释放资源，停止后台线程。
     * <p>
     * 此方法会停止后台线程并释放相关资源。
     * 在不再需要CaptureImageHelper时应调用此方法。
     * 调用后，此实例将不再可用，需要重新创建。
     * </p>
     */
    public void release()
    {
        if (backgroundThread != null)
        {
            backgroundThread.quitSafely();
            try
            {
                backgroundThread.join();
            }
            catch (InterruptedException e)
            {
                Log.e(TAG, "Error stopping background thread", e);
            }
        }
    }

}
