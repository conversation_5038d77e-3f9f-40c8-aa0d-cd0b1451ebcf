<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:background="@android:color/black">

    <!-- 顶部标题栏 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:background="#333333"
        android:padding="16dp"
        android:gravity="center_vertical">

        <Button
            android:id="@+id/btn_back"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="返回"
            android:textColor="@android:color/white"
            android:background="@android:color/transparent"
            android:textSize="14sp" />

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="图片视频对比"
            android:textColor="@android:color/white"
            android:textSize="18sp"
            android:gravity="center" />

        <View
            android:layout_width="60dp"
            android:layout_height="1dp" />

    </LinearLayout>

    <!-- 分屏对比区域 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:orientation="horizontal">

        <!-- 左侧图片区域 -->
        <FrameLayout
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1">

            <com.android.rockchip.camera2.integrated.browser.TpTextureView
                android:id="@+id/image_texture_view"
                android:layout_width="match_parent"
                android:layout_height="match_parent" />

            <TextView
                android:id="@+id/tv_image_info"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="bottom|start"
                android:background="#CC000000"
                android:textColor="#FFFFFF"
                android:textSize="12sp"
                android:padding="8dp"
                android:text="选中图片"
                android:maxLines="2"
                android:ellipsize="end" />

        </FrameLayout>

        <!-- 分隔线 -->
        <View
            android:layout_width="2dp"
            android:layout_height="match_parent"
            android:background="#333333" />

        <!-- 右侧实时预览区域 -->
        <FrameLayout
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:background="#000000">

            <com.android.rockchip.camera2.integrated.browser.TpTextureView
                android:id="@+id/texture_view"
                android:layout_width="match_parent"
                android:layout_height="match_parent" />

            <TextView
                android:id="@+id/tv_preview_info"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="bottom|end"
                android:background="#CC000000"
                android:textColor="#FFFFFF"
                android:textSize="12sp"
                android:padding="8dp"
                android:text="实时预览"
                android:maxLines="1" />

        </FrameLayout>

    </LinearLayout>

</LinearLayout>
