<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:padding="16dp"
    android:background="#F5F5F5">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <!-- TV模式设置标题 -->
        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="TV模式设置"
            android:textSize="20sp"
            android:textStyle="bold"
            android:textColor="#333333"
            android:layout_marginBottom="16dp" />

        <!-- 当前模式状态卡片 -->
        <androidx.cardview.widget.CardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            app:cardCornerRadius="8dp"
            app:cardElevation="4dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="当前模式状态"
                    android:textSize="16sp"
                    android:textStyle="bold"
                    android:textColor="#333333"
                    android:layout_marginBottom="8dp" />

                <TextView
                    android:id="@+id/tv_mode_status_text"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="当前模式：Camera模式"
                    android:textSize="14sp"
                    android:textColor="#666666"
                    android:layout_marginBottom="12dp" />

                <!-- TV模式开关 -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:gravity="center_vertical">

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="启用TV模式"
                        android:textSize="14sp"
                        android:textColor="#333333" />

                    <Switch
                        android:id="@+id/tv_mode_switch"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content" />

                </LinearLayout>

            </LinearLayout>

        </androidx.cardview.widget.CardView>

        <!-- 模式切换按钮卡片 -->
        <androidx.cardview.widget.CardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            app:cardCornerRadius="8dp"
            app:cardElevation="4dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="模式切换"
                    android:textSize="16sp"
                    android:textStyle="bold"
                    android:textColor="#333333"
                    android:layout_marginBottom="12dp" />

                <!-- 切换按钮容器 -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:gravity="center">

                    <Button
                        android:id="@+id/switch_to_camera_button"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:layout_margin="4dp"
                        android:text="切换到Camera模式"
                        android:textSize="12sp"
                        android:background="?android:attr/selectableItemBackground"
                        android:textColor="#2196F3" />

                    <Button
                        android:id="@+id/switch_to_tv_button"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:layout_margin="4dp"
                        android:text="切换到TV模式"
                        android:textSize="12sp"
                        android:background="?android:attr/selectableItemBackground"
                        android:textColor="#2196F3" />

                </LinearLayout>

            </LinearLayout>

        </androidx.cardview.widget.CardView>

        <!-- 模式说明卡片 -->
        <androidx.cardview.widget.CardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            app:cardCornerRadius="8dp"
            app:cardElevation="4dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="模式说明"
                    android:textSize="16sp"
                    android:textStyle="bold"
                    android:textColor="#333333"
                    android:layout_marginBottom="8dp" />

                <TextView
                    android:id="@+id/tv_mode_description_text"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="Camera模式：显示相机预览画面\n• 支持录制和拍照功能\n• 支持手势缩放控制\n• 支持RTSP推流"
                    android:textSize="14sp"
                    android:textColor="#666666"
                    android:lineSpacingExtra="2dp" />

            </LinearLayout>

        </androidx.cardview.widget.CardView>

        <!-- 使用提示卡片 -->
        <androidx.cardview.widget.CardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:cardCornerRadius="8dp"
            app:cardElevation="4dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="使用提示"
                    android:textSize="16sp"
                    android:textStyle="bold"
                    android:textColor="#333333"
                    android:layout_marginBottom="8dp" />

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="• 模式切换会自动停止当前的录制操作\n• TV模式下录制和拍照功能将被禁用\n• HDMI连接状态变化时会自动调整预览\n• 可以通过主界面的切换按钮快速切换模式"
                    android:textSize="14sp"
                    android:textColor="#666666"
                    android:lineSpacingExtra="2dp" />

            </LinearLayout>

        </androidx.cardview.widget.CardView>

    </LinearLayout>

</ScrollView>
