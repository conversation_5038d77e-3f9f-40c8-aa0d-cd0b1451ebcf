package com.touptek.video.internal.rtsp;

import android.content.Context;
import android.content.Intent;
import android.media.projection.MediaProjectionManager;
import android.os.Handler;
import android.os.Looper;
import android.util.Log;

import androidx.activity.result.ActivityResult;
import androidx.activity.result.ActivityResultLauncher;
import androidx.activity.result.contract.ActivityResultContracts;
import androidx.appcompat.app.AppCompatActivity;

import com.touptek.video.internal.TpVideoEncoder;
import com.touptek.video.internal.rtsp.config.RTSPConfig;
import com.touptek.video.internal.rtsp.service.ProjectionData;
import com.touptek.video.internal.rtsp.service.RTSPStreamer;

import java.util.function.Consumer;

/**
 * RTSP推流统一管理器
 * <p>
 * 这是RTSP推流功能的唯一访问点，负责协调所有推流相关操作。
 * 应用程序应该只与此类交互，而不直接使用底层实现类。
 * </p>
 * 
 * <p><b>主要功能：</b></p>
 * <ul>
 *   <li>摄像头视频推流 - 将摄像头捕获的视频通过RTSP协议推送</li>
 *   <li>屏幕录制推流 - 将设备屏幕内容通过RTSP协议推送</li>
 *   <li>自动权限管理 - 处理屏幕录制所需的系统权限</li>
 *   <li>网络接口选择 - 支持指定网络接口进行推流</li>
 *   <li>状态监控与回调 - 提供完整的推流状态通知机制</li>
 * </ul>
 * 
 * <p><b>使用示例：</b></p>
 * <pre>{@code
 * // 初始化
 * TpRtspManager rtspManager = TpRtspManager.getInstance()
 *     .initialize(activity)
 *     .setVideoEncoder(tpVideoEncoder)
 *     .onStreamStarted(url -> {
 *         // 处理推流开始事件，显示URL等
 *         showStreamingStatus("推流已开始: " + url);
 *     })
 *     .onStreamStopped(() -> {
 *         // 处理推流停止事件
 *         showStreamingStatus("推流已停止");
 *     })
 *     .onStreamError(error -> {
 *         // 处理推流错误
 *         showError("推流错误: " + error);
 *     });
 *     
 * // 开始推流
 * rtspManager.setStreamType(TpRtspManager.StreamType.CAMERA);
 * rtspManager.startStreaming();
 * 
 * // 停止推流
 * rtspManager.stopStreaming();
 * 
 * // 释放资源
 * rtspManager.release();
 * }</pre>
 */
public class TpRtspManager
{
    private static final String TAG = "TpRtspManager";

    /** 单例实例 */
    private static TpRtspManager instance;
    
    /** 应用程序上下文 */
    private Context context;
    
    /** RTSP配置信息 */
    private RTSPConfig config;
    
    /** RTSP流媒体推送器 */
    private RTSPStreamer streamer; // 将RTSPStreamer设为私有成员
    
    /** 视频编码器 */
    private TpVideoEncoder tpVideoEncoder;
    
    /** 当前推流类型 */
    private StreamType currentStreamType = StreamType.CAMERA;
    
    /** 屏幕录制权限数据 */
    private ProjectionData projectionData;
    
    /** 是否已初始化 */
    private boolean initialized = false;
    
    /** 是否正在推流 */
    private boolean streaming = false;
    
    /** 选择的网络接口名称 */
    private String networkInterface = null;
    
    /** 屏幕捕获启动器 */
    private ActivityResultLauncher<Intent> screenCaptureLauncher;
    
    /** 主线程Handler，用于回调操作 */
    private final Handler mainHandler = new Handler(Looper.getMainLooper());
    
    /** 流开始回调处理器 */
    private Consumer<String> onStreamStartedHandler;
    
    /** 流停止回调处理器 */
    private Runnable onStreamStoppedHandler;
    
    /** 流错误回调处理器 */
    private Consumer<String> onStreamErrorHandler;
    
    /** 权限授权回调处理器 */
    private Consumer<String> onPermissionGrantedHandler;
    
    /** 是否正在等待权限授权 */
    private boolean waitingForPermission = false;
    
    /**
     * 私有的StreamStateListener实现，用于转发到回调处理器
     */
    private final StreamStateListener streamListener = new StreamStateListener() 
    {
        @Override
        public void onStreamStarted(String url) 
        {
            if (onStreamStartedHandler != null) 
            {
                mainHandler.post(() -> onStreamStartedHandler.accept(url));
            }
        }

        @Override
        public void onStreamStopped() 
        {
            if (onStreamStoppedHandler != null) 
            {
                mainHandler.post(() -> onStreamStoppedHandler.run());
            }
        }

        @Override
        public void onStreamError(String errorMessage) 
        {
            if (onStreamErrorHandler != null) 
            {
                mainHandler.post(() -> onStreamErrorHandler.accept(errorMessage));
            }
        }
        
        @Override
        public void onPermissionGranted(String message) 
        {
            if (onPermissionGrantedHandler != null) 
            {
                mainHandler.post(() -> onPermissionGrantedHandler.accept(message));
            }
        }
    };

    /**
     * 私有构造函数，防止外部实例化
     */
    private TpRtspManager()
    {
    }

    /**
     * 获取RTSPManager单例实例
     * 
     * @return RTSPManager单例实例
     */
    public static synchronized TpRtspManager getInstance()
    {
        if (instance == null) 
        {
            instance = new TpRtspManager();
        }
        return instance;
    }

    /**
     * 初始化RTSPManager
     * 必须在使用其他方法前调用此方法
     * 
     * @param activity 关联的Activity实例，用于权限请求
     * @return RTSPManager实例，用于链式调用
     */
    public TpRtspManager initialize(AppCompatActivity activity)
    {
        if (initialized) return this;

        this.context = activity.getApplicationContext();
        this.config = RTSPConfig.createDefaultConfig();

        // 初始化屏幕捕获权限请求
        screenCaptureLauncher = activity.registerForActivityResult(
            new ActivityResultContracts.StartActivityForResult(),
                this::handleScreenCaptureResult
        );

        initialized = true;
        return this;
    }

    /**
     * 设置RTSP配置
     * 
     * @param config RTSP配置对象
     * @return RTSPManager实例，用于链式调用
     */
    public TpRtspManager setConfig(RTSPConfig config)
    {
        this.config = config;
        return this;
    }

    /**
     * 设置视频编码器
     * 当推流类型为CAMERA时必须设置
     * 
     * @param tpVideoEncoder 视频编码器实例
     * @return RTSPManager实例，用于链式调用
     */
    public TpRtspManager setVideoEncoder(TpVideoEncoder tpVideoEncoder)
    {
        this.tpVideoEncoder = tpVideoEncoder;
        return this;
    }
    
    /**
     * 设置流开始的回调处理器
     * 
     * @param handler 处理流URL的回调，参数为完整RTSP URL
     * @return RTSPManager实例，用于链式调用
     */
    public TpRtspManager onStreamStarted(Consumer<String> handler)
    {
        this.onStreamStartedHandler = handler;
        return this;
    }
    
    /**
     * 设置流停止的回调处理器
     * 
     * @param handler 处理流停止的回调
     * @return RTSPManager实例，用于链式调用
     */
    public TpRtspManager onStreamStopped(Runnable handler)
    {
        this.onStreamStoppedHandler = handler;
        return this;
    }
    
    /**
     * 设置流错误的回调处理器
     * 
     * @param handler 处理错误消息的回调，参数为错误信息
     * @return RTSPManager实例，用于链式调用
     */
    public TpRtspManager onStreamError(Consumer<String> handler)
    {
        this.onStreamErrorHandler = handler;
        return this;
    }
    
    /**
     * 设置权限获取成功的回调处理器
     * 
     * @param handler 处理权限消息的回调，参数为成功消息
     * @return RTSPManager实例，用于链式调用
     */
    public TpRtspManager onPermissionGranted(Consumer<String> handler)
    {
        this.onPermissionGrantedHandler = handler;
        return this;
    }

    /**
     * 检查是否已获取屏幕录制权限
     * 
     * @return 是否已获取屏幕录制权限
     */
    public boolean hasScreenCapturePermission() 
    {
        return projectionData != null && projectionData.isValid();
    }

    /**
     * 显式请求屏幕录制权限
     * 应用程序可以在需要时调用此方法预先获取权限
     * 
     * @return 是否成功发起权限请求
     */
    public boolean requestScreenPermission() 
    {
        if (hasScreenCapturePermission()) 
        {
            // 已有权限，通知已获取
            if (streamListener != null) 
            {
                streamListener.onPermissionGranted("屏幕捕获权限已获取");
            }
            return true;
        }
        
        // 请求新权限
        Log.d(TAG, "Explicitly requesting screen capture permission");
        requestScreenCapturePermission();
        return true;
    }

    /**
     * 设置推流类型
     * 不会自动请求权限，需要用户显式调用requestScreenPermission()
     * 
     * @param type 推流类型，SCREEN或CAMERA
     * @return RTSPManager实例，用于链式调用
     */
    public TpRtspManager setStreamType(StreamType type)
    {
        if (currentStreamType == type) return this;

        if (streaming) stopStreaming();
        currentStreamType = type;
        
        // 重置streamer实例，确保使用新的配置
        if (streamer != null) 
        {
            streamer = null;
        }
        
        return this;
    }

    /**
     * 设置要使用的网络接口
     * 
     * @param networkInterface 网络接口名称，如"wlan0"或"eth0"
     * @return RTSPManager实例，用于链式调用
     */
    public TpRtspManager setNetworkInterface(String networkInterface) {
        this.networkInterface = networkInterface;
        Log.d(TAG, "设置网络接口: " + networkInterface);
        return this;
    }

    /**
     * 获取当前设置的网络接口
     * 
     * @return 当前设置的网络接口，如果未设置则返回null
     */
    public String getNetworkInterface() {
        return networkInterface;
    }

    /**
     * 开始流媒体推送
     * 需要先设置推流类型和相关参数
     * 
     * @return 是否成功启动推流
     */
    public boolean startStreaming() 
    {
        if (!initialized) 
        {
            Log.e(TAG, "RTSPManager未初始化，无法启动推流");
            return false;
        }
        
        if (streaming) 
        {
            Log.w(TAG, "已经在推流中，忽略重复启动请求");
            return true;
        }

        try 
        {
            // 初始化推流器
            initStreamer();
            
            // 配置网络接口（如果指定了）
            if (networkInterface != null) {
                // 更新RTSP URL，将IP设置为指定网络接口的IP
                String ipAddress = getIpAddressForInterface(networkInterface);
                if (ipAddress != null && !ipAddress.isEmpty()) {
                    config.setHost(ipAddress);
                    Log.d(TAG, "使用网络接口 " + networkInterface + " 的IP地址: " + ipAddress);
                } else {
                    Log.w(TAG, "无法获取网络接口 " + networkInterface + " 的IP地址，使用默认IP");
                }
            }
            
            if (currentStreamType == StreamType.CAMERA) 
            {
                // 摄像头流需要视频编码器
                if (tpVideoEncoder == null)
                {
                    Log.e(TAG, "视频编码器未设置，无法启动摄像头推流");
                    return false;
                }
                
                // 启动摄像头推流
                streamer.startCameraStreaming(tpVideoEncoder, config, streamListener);
                
            } 
            else 
            { // SCREEN
                // 屏幕流需要投影数据
                if (projectionData == null || !projectionData.isValid()) 
                {
                    Log.e(TAG, "未获取屏幕录制权限，无法启动屏幕推流");
                    return false;
                }
                
                // 启动屏幕推流
                streamer.startScreenStreaming(projectionData, config, streamListener);
            }
            
            streaming = true;
            return true;
            
        } 
        catch (Exception e) 
        {
            Log.e(TAG, "启动推流失败: " + e.getMessage(), e);
            if (streamListener != null) 
            {
                streamListener.onStreamError("启动推流失败: " + e.getMessage());
            }
            return false;
        }
    }

    /**
     * 获取指定网络接口的IP地址
     * 
     * @param interfaceName 网络接口名称，如"wlan0"
     * @return 网络接口的IPv4地址，如果找不到则返回null
     */
    private String getIpAddressForInterface(String interfaceName) {
        try {
            java.util.Enumeration<java.net.NetworkInterface> networkInterfaces = 
                java.net.NetworkInterface.getNetworkInterfaces();
            
            while (networkInterfaces.hasMoreElements()) {
                java.net.NetworkInterface networkInterface = networkInterfaces.nextElement();
                if (networkInterface.getName().equals(interfaceName)) {
                    java.util.Enumeration<java.net.InetAddress> addresses = networkInterface.getInetAddresses();
                    while (addresses.hasMoreElements()) {
                        java.net.InetAddress address = addresses.nextElement();
                        if (!address.isLoopbackAddress() && address instanceof java.net.Inet4Address) {
                            return address.getHostAddress();
                        }
                    }
                }
            }
        } catch (Exception e) {
            Log.e(TAG, "获取网络接口IP地址失败: " + e.getMessage(), e);
        }
        return null;
    }

    /**
     * 停止推流
     */
    public void stopStreaming() 
    {
        if (!streaming || streamer == null) return;

        streamer.stopStream();
        streaming = false;
    }

    /**
     * 请求屏幕捕获权限
     * 该方法会弹出系统权限对话框
     */
    private void requestScreenCapturePermission() 
    {
        MediaProjectionManager projectionManager =
            (MediaProjectionManager) context.getSystemService(Context.MEDIA_PROJECTION_SERVICE);
        Intent captureIntent = projectionManager.createScreenCaptureIntent();
        screenCaptureLauncher.launch(captureIntent);
    }

    /**
     * 处理屏幕捕获结果
     * 
     * @param result 屏幕捕获权限结果
     */
    private void handleScreenCaptureResult(ActivityResult result) 
    {
        if (result.getResultCode() == AppCompatActivity.RESULT_OK) 
        {
            projectionData = new ProjectionData(result.getResultCode(), result.getData());
            
            // 权限获取成功回调
            if (streamListener != null) 
            {
                streamListener.onPermissionGranted("屏幕捕获权限已获取");
            }
            
            // 不再自动开始推流，需要用户显式调用startStreaming()
            waitingForPermission = false;
            Log.d(TAG, "Screen capture permission granted. Call startStreaming() to begin streaming");
        } 
        else 
        {
            waitingForPermission = false;
            if (streamListener != null) 
            {
                streamListener.onStreamError("屏幕捕获权限被拒绝");
            }
        }
    }

    /**
     * 初始化推流器
     */
    private void initStreamer() 
    {
        streamer = new RTSPStreamer(
            context,
            config,
            currentStreamType,
            projectionData,
                tpVideoEncoder,
            streamListener
        );
    }

    /**
     * 获取当前推流URL
     * 
     * @return 推流URL，如果未推流则返回null
     */
    public String getStreamUrl() 
    {
        return streamer != null && streamer.isStreaming() ? streamer.getRtspUrl() : null;
    }

    /**
     * 获取当前推流类型
     * 
     * @return 当前推流类型
     */
    public StreamType getCurrentStreamType() 
    {
        return currentStreamType;
    }

    /**
     * 判断是否正在推流
     * 
     * @return 是否正在推流
     */
    public boolean isStreaming() 
    {
        return streaming;
    }

    /**
     * 释放所有资源
     * 在不再需要推流功能时调用
     */
    public void release() 
    {
        if (streaming) stopStreaming();
        streamer = null;
        initialized = false;
    }

    /**
     * 推流类型枚举
     * <p>
     * 定义了支持的RTSP推流源类型，可以是摄像头捕获或屏幕录制。
     * 不同的推流类型需要不同的初始化步骤和权限。
     * </p>
     */
    public enum StreamType 
    {
        /** 
         * 屏幕录制推流
         * <p>
         * 将设备屏幕内容作为视频源进行RTSP推流。
         * 使用此类型需要先获取屏幕录制权限。
         * </p>
         */
        SCREEN, 
        
        /** 
         * 摄像头推流
         * <p>
         * 将摄像头捕获的视频作为源进行RTSP推流。
         * 使用此类型需要先设置VideoEncoder实例。
         * </p>
         */
        CAMERA
    }

    /**
     * 推流状态监听接口
     * 仅供RTSPManager内部使用
     */
    public interface StreamStateListener 
    {
        /**
         * 推流开始回调
         * 
         * @param url 推流URL
         */
        void onStreamStarted(String url);
        
        /**
         * 推流停止回调
         */
        void onStreamStopped();
        
        /**
         * 推流错误回调
         * 
         * @param errorMessage 错误信息
         */
        void onStreamError(String errorMessage);
        
        /**
         * 权限获取成功回调
         * 
         * @param message 成功信息
         */
        void onPermissionGranted(String message);
    }
}