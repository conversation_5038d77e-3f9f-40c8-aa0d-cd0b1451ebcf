package com.touptek.video.internal.rtsp.encoder;

import android.hardware.display.DisplayManager;
import android.hardware.display.VirtualDisplay;
import android.media.MediaCodec;
import android.media.MediaCodecInfo;
import android.media.MediaFormat;
import android.media.projection.MediaProjection;
import android.util.Log;
import android.view.Surface;

import com.pedro.rtspserver.RtspServer;

import java.nio.ByteBuffer;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * 屏幕视频编码器
 * 负责捕获屏幕内容并编码为H.264格式
 */
public class ScreenVideoEncoder {
    private static final String TAG = "ScreenVideoEncoder";
    private static final String MIME_TYPE = MediaFormat.MIMETYPE_VIDEO_AVC;  // H.264编码
    
    private final MediaProjection mediaProjection;
    private final int width;
    private final int height;
    private final int frameRate;
    private final int bitrate;
    private final int keyFrameInterval;
    
    /**
     * H.264编码器
     */
    private MediaCodec encoder;
    
    /**
     * 虚拟显示，用于捕获屏幕
     */
    private VirtualDisplay virtualDisplay;
    
    /**
     * 编码器输入表面
     */
    private Surface inputSurface;
    
    /**
     * 缓冲区信息
     */
    private final MediaCodec.BufferInfo bufferInfo = new MediaCodec.BufferInfo();
    
    /**
     * 是否正在编码
     */
    private final AtomicBoolean isEncoding = new AtomicBoolean(false);
    
    /**
     * 编码线程
     */
    private Thread encodingThread;

    private ScreenVideoEncoder(Builder builder) {
        this.mediaProjection = builder.mediaProjection;
        this.width = builder.width;
        this.height = builder.height;
        this.frameRate = builder.frameRate;
        this.bitrate = builder.bitrate;
        this.keyFrameInterval = builder.keyFrameInterval;
    }

    public static Builder builder(MediaProjection mediaProjection) {
        return new Builder(mediaProjection);
    }

    public static class Builder {
        private final MediaProjection mediaProjection;
        private int width = 1280;
        private int height = 720;
        private int frameRate = 30;
        private int bitrate = 4000000; // 4 Mbps
        private int keyFrameInterval = 1;

        private Builder(MediaProjection mediaProjection) {
            this.mediaProjection = mediaProjection;
        }

        public Builder setResolution(int width, int height) {
            this.width = width;
            this.height = height;
            return this;
        }

        public Builder setFrameRate(int frameRate) {
            this.frameRate = frameRate;
            return this;
        }

        public Builder setBitrate(int bitrate) {
            this.bitrate = bitrate;
            return this;
        }

        public Builder setKeyFrameInterval(int keyFrameInterval) {
            this.keyFrameInterval = keyFrameInterval;
            return this;
        }

        public ScreenVideoEncoder build() {
            return new ScreenVideoEncoder(this);
        }
    }
    
    /**
     * 开始编码
     * @param rtspServer RTSP服务器
     * @param audioEncoder 音频编码器
     */
    public void startEncoding(RtspServer rtspServer, AudioEncoder audioEncoder) {
        if (isEncoding.get()) {
            Log.w(TAG, "Encoder is already running");
            return;
        }
        
        try {
            // 创建视频格式
            MediaFormat videoFormat = MediaFormat.createVideoFormat(MIME_TYPE, width, height);
            // 设置编码参数
            videoFormat.setInteger(
                MediaFormat.KEY_COLOR_FORMAT,
                MediaCodecInfo.CodecCapabilities.COLOR_FormatSurface
            );
            videoFormat.setInteger(MediaFormat.KEY_BIT_RATE, bitrate);
            videoFormat.setInteger(MediaFormat.KEY_FRAME_RATE, frameRate);
            videoFormat.setInteger(MediaFormat.KEY_I_FRAME_INTERVAL, keyFrameInterval);
            videoFormat.setInteger(
                MediaFormat.KEY_BITRATE_MODE,
                MediaCodecInfo.EncoderCapabilities.BITRATE_MODE_VBR
            );
            
            // 创建编码器
            encoder = MediaCodec.createEncoderByType(MIME_TYPE);
            encoder.configure(videoFormat, null, null, MediaCodec.CONFIGURE_FLAG_ENCODE);
            inputSurface = encoder.createInputSurface();
            encoder.start();
            
            // 创建虚拟显示
            virtualDisplay = mediaProjection.createVirtualDisplay(
                "screen-capture",
                width,
                height,
                1, // 屏幕密度
                DisplayManager.VIRTUAL_DISPLAY_FLAG_AUTO_MIRROR,
                inputSurface,
                null,
                null
            );
            
            // 开始编码线程
            isEncoding.set(true);
            
            encodingThread = new Thread(() -> encodeVideoLoop(rtspServer, audioEncoder));
            encodingThread.start();
            
            Log.d(TAG, "Video encoder started");
        } catch (Exception e) {
            Log.e(TAG, "Failed to start video encoder", e);
            release();
        }
    }
    
    /**
     * 停止编码
     */
    public void stopEncoding() {
        if (!isEncoding.get()) return;
        
        isEncoding.set(false);
        if (encodingThread != null) {
            try {
                encodingThread.join(1000);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
        }
        release();
        
        Log.d(TAG, "Video encoder stopped");
    }
    
    /**
     * 编码循环
     */
    private void encodeVideoLoop(RtspServer rtspServer, AudioEncoder audioEncoder) {
        MediaCodec encoder = this.encoder;
        if (encoder == null) return;
        
        ByteBuffer spsBuffer = null;
        ByteBuffer ppsBuffer = null;
        boolean formatConfigured = false;
        
        try {
            final long startTime = System.nanoTime();
            
            while (isEncoding.get()) {
                // 处理音频
                if (audioEncoder != null) {
                    long timestamp = (System.nanoTime() - startTime) / 1000;
                    audioEncoder.encodeAudio(rtspServer, timestamp);
                }
                
                // 从编码器获取输出
                int outputBufferIndex = encoder.dequeueOutputBuffer(bufferInfo, 10000);
                
                if (outputBufferIndex == MediaCodec.INFO_OUTPUT_FORMAT_CHANGED && !formatConfigured) {
                    // 编码器输出格式变化
                    MediaFormat format = encoder.getOutputFormat();
                    Log.d(TAG, "Encoder output format changed: " + format);
                    
                    // 获取SPS和PPS
                    spsBuffer = format.getByteBuffer("csd-0");
                    ppsBuffer = format.getByteBuffer("csd-1");
                    
                    if (spsBuffer != null && ppsBuffer != null) {
                        // 设置视频参数到RTSP服务器
                        rtspServer.setVideoInfo(spsBuffer, ppsBuffer, null);
                        formatConfigured = true;
                    }
                } else if (outputBufferIndex >= 0) {
                    // 有可用的输出数据
                    ByteBuffer outputBuffer = encoder.getOutputBuffer(outputBufferIndex);
                    
                    if (outputBuffer != null) {
                        // 计算时间戳
                        long timestamp = (System.nanoTime() - startTime) / 1000;
                        bufferInfo.presentationTimeUs = timestamp;
                        
                        // 发送视频数据
                        rtspServer.sendVideo(outputBuffer, bufferInfo);
                    }
                    
                    // 释放输出缓冲区
                    encoder.releaseOutputBuffer(outputBufferIndex, false);
                } else if (outputBufferIndex < 0) {
                    // 没有可用的输出数据，稍微休息一下
                    try {
                        Thread.sleep(10);
                    } catch (InterruptedException e) {
                        Thread.currentThread().interrupt();
                    }
                }
            }
        } catch (Exception e) {
            if (isEncoding.get()) {
                Log.e(TAG, "Error in encoding loop", e);
            }
        } finally {
            release();
        }
    }
    
    /**
     * 释放资源
     */
    private void release() {
        try {
            if (virtualDisplay != null) {
                virtualDisplay.release();
                virtualDisplay = null;
            }
            
            if (inputSurface != null) {
                inputSurface.release();
                inputSurface = null;
            }
            
            if (encoder != null) {
                encoder.stop();
                encoder.release();
                encoder = null;
            }
        } catch (Exception e) {
            Log.e(TAG, "Error releasing resources", e);
        }
    }
}
