package com.android.rockchip.camera2.separated.dialogs;

import android.app.Dialog;
import android.content.Context;
import android.content.Intent;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.view.View;
import android.view.Window;
import android.widget.AdapterView;
import android.widget.ArrayAdapter;
import android.widget.Button;
import android.widget.ImageButton;
import android.widget.RadioButton;
import android.widget.RadioGroup;
import android.widget.Spinner;
import android.widget.TextView;
import android.widget.Toast;

import androidx.activity.result.ActivityResultLauncher;

import com.touptek.utils.TpNetworkMonitor;
import com.touptek.video.internal.service.TpStreamingService;
import com.android.rockchip.mediacodecnew.R;

import java.util.ArrayList;
import java.util.List;

public class NetworkSettingsDialog extends Dialog implements TpNetworkMonitor.NetworkStateListener {

    private static final String TAG = "NetworkSettingsDialog";

    // 网络设置相关控件
    private TextView ethernetStatusText;
    private TextView wifiStatusText;
    private TextView hotspotStatusText;
    private TextView networkInfoText;
    private Button wifiButton;
    private Button hotspotButton;

    // RTSP推流控制相关控件
    private TextView rtspStatusText;
    private TextView rtspUrlText;
    private Spinner networkInterfaceSpinner;
    private RadioGroup streamTypeRadioGroup;
    private RadioButton cameraStreamRadio;
    private RadioButton screenStreamRadio;
    private Button startRtspButton;
    private Button stopRtspButton;

    private TpNetworkMonitor tpNetworkMonitor;
    private boolean isWifiEnabled = false;
    private final ActivityResultLauncher<Intent> wifiPanelLauncher;
    
    // TpctrlService引用
    private TpStreamingService tpStreamingService;
    
    // 网络接口列表
    private List<TpNetworkMonitor.NetworkInterfaceInfo> networkInterfaces = new ArrayList<>();
    private String selectedNetworkInterface = null;
    private boolean isRtspStreaming = false;

    public NetworkSettingsDialog(Context context, ActivityResultLauncher<Intent> wifiPanelLauncher, TpStreamingService tpStreamingService) {
        super(context);
        this.wifiPanelLauncher = wifiPanelLauncher;
        this.tpStreamingService = tpStreamingService;
        // 使用自定义布局
        requestWindowFeature(Window.FEATURE_NO_TITLE);
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.network_settings);
        
        // 初始化网络视图
        initNetworkViews();
        
        // 初始化RTSP控制视图
        initRtspControlViews();
        
        // 初始化网络管理器
        tpNetworkMonitor = new TpNetworkMonitor(getContext(), this, wifiPanelLauncher);
        
        // 设置WiFi按钮点击事件
        wifiButton.setOnClickListener(v -> {
            tpNetworkMonitor.toggleWifi(isWifiEnabled);
            if (isWifiEnabled) {
                networkInfoText.setText("正在断开WiFi连接...");
            } else {
                networkInfoText.setText("请在系统设置中选择并连接WiFi");
            }
        });
        
        // 设置热点按钮点击事件
        hotspotButton.setOnClickListener(v -> {
            tpNetworkMonitor.openHotspotSettings();
            networkInfoText.setText("请在系统设置中配置热点");
        });
        
        // 设置关闭按钮点击事件
        ImageButton closeButton = findViewById(R.id.btn_close_network);
        closeButton.setOnClickListener(v -> dismiss());
        
        // 设置对话框大小
        getWindow().setLayout(
                getContext().getResources().getDisplayMetrics().widthPixels * 2 / 3,
                getContext().getResources().getDisplayMetrics().heightPixels * 3 / 4
        );
    }
    
    /**
     * 初始化网络相关视图
     */
    private void initNetworkViews() {
        ethernetStatusText = findViewById(R.id.ethernet_status_text);
        wifiStatusText = findViewById(R.id.wifi_status_text);
        hotspotStatusText = findViewById(R.id.hotspot_status_text);
        networkInfoText = findViewById(R.id.network_info_text);
        wifiButton = findViewById(R.id.wifi_button);
        hotspotButton = findViewById(R.id.hotspot_button);
    }
    
    /**
     * 初始化RTSP控制相关视图
     */
    private void initRtspControlViews() {
        rtspStatusText = findViewById(R.id.rtsp_status_text);
        rtspUrlText = findViewById(R.id.rtsp_url_text);
        networkInterfaceSpinner = findViewById(R.id.network_interface_spinner);
        streamTypeRadioGroup = findViewById(R.id.stream_type_radio_group);
        cameraStreamRadio = findViewById(R.id.radio_camera_stream);
        screenStreamRadio = findViewById(R.id.radio_screen_stream);
        startRtspButton = findViewById(R.id.start_rtsp_button);
        stopRtspButton = findViewById(R.id.stop_rtsp_button);
        
        // 检查tpctrlService是否可用
        if (tpStreamingService == null) {
            disableRtspControls("RTSP服务不可用");
            return;
        }
        
        // 设置RTSP状态
        updateRtspStatus();
        
        // 加载网络接口列表
        loadNetworkInterfaces();
        
        // 设置网络接口选择事件
        networkInterfaceSpinner.setOnItemSelectedListener(new AdapterView.OnItemSelectedListener() {
            @Override
            public void onItemSelected(AdapterView<?> parent, View view, int position, long id) {
                if (position >= 0 && position < networkInterfaces.size()) {
                    selectedNetworkInterface = networkInterfaces.get(position).getName();
                    updateStartButtonState();
                }
            }

            @Override
            public void onNothingSelected(AdapterView<?> parent) {
                selectedNetworkInterface = null;
                updateStartButtonState();
            }
        });
        
        // 设置流类型选择事件
        streamTypeRadioGroup.setOnCheckedChangeListener((group, checkedId) -> {
            updateStartButtonState();
        });
        
        // 设置启动推流按钮点击事件
        startRtspButton.setOnClickListener(v -> {
            startRtspStreaming();
        });
        
        // 设置停止推流按钮点击事件
        stopRtspButton.setOnClickListener(v -> {
            stopRtspStreaming();
        });
    }
    
    /**
     * 启动RTSP推流
     */
    private void startRtspStreaming() {
        if (tpStreamingService == null || selectedNetworkInterface == null) {
            Toast.makeText(getContext(), "RTSP服务或网络接口不可用", Toast.LENGTH_SHORT).show();
            return;
        }
        
        networkInfoText.setText("正在启动RTSP推流，请稍候...");
        startRtspButton.setEnabled(false);
        
        // 获取选择的流类型
        TpStreamingService.StreamType streamType = cameraStreamRadio.isChecked() ?
                TpStreamingService.StreamType.CAMERA : TpStreamingService.StreamType.SCREEN;
        
        // 直接启动推流（内部会杀死并重启tpctrl进程）
        new Thread(() -> {
            final boolean success = tpStreamingService.startRtspManually(streamType, selectedNetworkInterface);
            
            // 在UI线程中更新界面
            new Handler(Looper.getMainLooper()).post(() -> {
                if (success) {
                    isRtspStreaming = true;
                    startRtspButton.setEnabled(false);
                    stopRtspButton.setEnabled(true);
                    networkInterfaceSpinner.setEnabled(false);
                    streamTypeRadioGroup.setEnabled(false);
                    
                    String streamTypeName = streamType == TpStreamingService.StreamType.CAMERA ? "摄像头流" : "屏幕流";
                    networkInfoText.setText("已成功启动" + streamTypeName + "推流，网络接口: " + selectedNetworkInterface);
                    
                    // 更新RTSP状态
                    updateRtspStatus();
                } else {
                    startRtspButton.setEnabled(true);
                    Toast.makeText(getContext(), "启动RTSP推流失败", Toast.LENGTH_SHORT).show();
                    networkInfoText.setText("启动RTSP推流失败，请检查系统日志");
                }
            });
        }).start();
    }
    
    /**
     * 停止RTSP推流
     */
    private void stopRtspStreaming() {
        if (tpStreamingService == null) {
            return;
        }
        
        networkInfoText.setText("正在停止RTSP推流，请稍候...");
        stopRtspButton.setEnabled(false);
        
        // 在单独线程中执行停止操作
        new Thread(() -> {
            final boolean success = tpStreamingService.stopRtspManually();
            
            // 在UI线程中更新界面
            new Handler(Looper.getMainLooper()).post(() -> {
                if (success) {
                    isRtspStreaming = false;
                    startRtspButton.setEnabled(true);
                    stopRtspButton.setEnabled(false);
                    networkInterfaceSpinner.setEnabled(true);
                    streamTypeRadioGroup.setEnabled(true);
                    
                    networkInfoText.setText("已停止RTSP推流");
                    
                    // 更新RTSP状态
                    updateRtspStatus();
                } else {
                    stopRtspButton.setEnabled(true);
                    Toast.makeText(getContext(), "停止RTSP推流失败", Toast.LENGTH_SHORT).show();
                    networkInfoText.setText("停止RTSP推流失败，请检查系统日志");
                }
            });
        }).start();
    }
    
    /**
     * 加载网络接口列表
     */
    private void loadNetworkInterfaces() {
        if (tpNetworkMonitor == null) {
            return;
        }
        
        networkInterfaces = tpNetworkMonitor.getAvailableNetworkInterfaces();
        
        if (networkInterfaces.isEmpty()) {
            // 没有可用的网络接口
            networkInfoText.setText("未检测到可用的网络接口");
            disableRtspControls("未检测到可用的网络接口");
            return;
        }
        
        // 准备显示数据
        List<String> displayList = new ArrayList<>();
        for (TpNetworkMonitor.NetworkInterfaceInfo info : networkInterfaces) {
            displayList.add(info.getDescription() + " (" + info.getName() + "): " + info.getIpAddress());
        }
        
        // 设置下拉列表适配器
        ArrayAdapter<String> adapter = new ArrayAdapter<>(
                getContext(), 
                android.R.layout.simple_spinner_item, 
                displayList);
        adapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item);
        networkInterfaceSpinner.setAdapter(adapter);
        
        // 默认选择第一个
        if (!networkInterfaces.isEmpty()) {
            selectedNetworkInterface = networkInterfaces.get(0).getName();
            networkInterfaceSpinner.setSelection(0);
        }
        
        updateStartButtonState();
    }
    
    /**
     * 更新启动按钮状态
     */
    private void updateStartButtonState() {
        if (tpStreamingService == null || selectedNetworkInterface == null || isRtspStreaming) {
            startRtspButton.setEnabled(false);
        } else {
            startRtspButton.setEnabled(true);
        }
    }
    
    /**
     * 更新RTSP状态显示
     */
    private void updateRtspStatus() {
        if (tpStreamingService == null) {
            rtspStatusText.setText("RTSP状态：服务不可用");
            rtspUrlText.setText("RTSP URL：");
            return;
        }
        
        boolean isStreaming = tpStreamingService.isStreaming();
        isRtspStreaming = isStreaming;
        
        if (isStreaming) {
            String streamType = tpStreamingService.getCurrentStreamType() == TpStreamingService.StreamType.CAMERA ?
                    "摄像头流" : "屏幕流";
            rtspStatusText.setText("RTSP状态：推流中 (" + streamType + ")");
            
            String url = tpStreamingService.getStreamUrl();
            rtspUrlText.setText("RTSP URL：" + (url != null ? url : "未知"));
            
            startRtspButton.setEnabled(false);
            stopRtspButton.setEnabled(true);
            networkInterfaceSpinner.setEnabled(false);
            streamTypeRadioGroup.setEnabled(false);
        } else {
            rtspStatusText.setText("RTSP状态：未启动");
            rtspUrlText.setText("RTSP URL：");
            
            startRtspButton.setEnabled(selectedNetworkInterface != null);
            stopRtspButton.setEnabled(false);
            networkInterfaceSpinner.setEnabled(true);
            streamTypeRadioGroup.setEnabled(true);
        }
    }
    
    /**
     * 禁用RTSP控制
     */
    private void disableRtspControls(String reason) {
        rtspStatusText.setText("RTSP状态：" + reason);
        rtspUrlText.setText("RTSP URL：");
        startRtspButton.setEnabled(false);
        stopRtspButton.setEnabled(false);
        networkInterfaceSpinner.setEnabled(false);
        streamTypeRadioGroup.setEnabled(false);
    }
    
    @Override
    protected void onStart() {
        super.onStart();
        // 开始网络监控
        if (tpNetworkMonitor != null) {
            tpNetworkMonitor.startMonitoring();
        }
        
        // 刷新RTSP状态
        updateRtspStatus();
        
        // 重新加载网络接口
        loadNetworkInterfaces();
    }
    
    @Override
    protected void onStop() {
        super.onStop();
        // 停止网络监控
        if (tpNetworkMonitor != null) {
            tpNetworkMonitor.stopMonitoring();
        }
    }

    @Override
    public void onWifiStateChanged(boolean isConnected, String ssid) {
        isWifiEnabled = isConnected;
        if (isConnected) {
            wifiStatusText.setText("WIFI状态：已连接");
            wifiButton.setText("断开WIFI");
            
            // 显示当前连接的WiFi信息
            networkInfoText.setText("已连接到: " + ssid);
            
            // 刷新网络接口列表
            loadNetworkInterfaces();
        } else {
            wifiStatusText.setText("WIFI状态：未连接");
            wifiButton.setText("连接WIFI");
            
            // 如果热点未开启，清空信息文本
            String currentInfo = networkInfoText.getText().toString();
            if (!currentInfo.contains("热点") && !currentInfo.contains("请在系统设置")) {
                networkInfoText.setText("");
            }
            
            // 刷新网络接口列表
            loadNetworkInterfaces();
        }
    }

    @Override
    public void onEthernetStateChanged(boolean isConnected) {
        if (isConnected) {
            ethernetStatusText.setText("以太网状态：已连接");
            
            // 刷新网络接口列表
            loadNetworkInterfaces();
        } else {
            ethernetStatusText.setText("以太网状态：未连接");
            
            // 刷新网络接口列表
            loadNetworkInterfaces();
        }
    }

    @Override
    public void onHotspotStateChanged(boolean isEnabled, String hotspotInfo) {
        if (isEnabled) {
            hotspotStatusText.setText("热点状态：已开启");
            hotspotButton.setText("热点设置");
            
            if (!hotspotInfo.isEmpty()) {
                networkInfoText.setText(hotspotInfo);
            }
            
            // 刷新网络接口列表
            loadNetworkInterfaces();
        } else {
            hotspotStatusText.setText("热点状态：未开启");
            hotspotButton.setText("开启热点");
            
            // 如果WiFi未连接且信息文本显示的是热点信息，清空信息文本
            if (!isWifiEnabled && networkInfoText.getText().toString().contains("热点")) {
                networkInfoText.setText("");
            }
            
            // 刷新网络接口列表
            loadNetworkInterfaces();
        }
    }
} 