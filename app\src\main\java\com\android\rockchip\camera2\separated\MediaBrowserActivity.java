package com.android.rockchip.camera2.separated;

import android.content.Intent;
import android.os.Bundle;

import androidx.appcompat.app.AppCompatActivity;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.android.rockchip.camera2.integrated.browser.TpVideoPlayerActivity;
import com.touptek.utils.TpFileManager;
import com.android.rockchip.mediacodecnew.R;

import java.io.File;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * MediaBrowserActivity 类用于显示媒体文件的网格视图，
 * 并允许用户点击查看视频或图片。
 */
public class MediaBrowserActivity extends AppCompatActivity
{
    /* 媒体文件列表 */
    private List<File> mediaFiles = new ArrayList<>();
    
    /* 支持的图片格式 */
    private static final Set<String> SUPPORTED_IMAGE_FORMATS = new HashSet<>(
            Arrays.asList(".jpg", ".jpeg", ".png", ".bmp",".gif",".tiff",".tif"));
    
    /* 支持的视频格式 */
    private static final Set<String> SUPPORTED_VIDEO_FORMATS = new HashSet<>(
            Arrays.asList(".mp4"));

    /**
     * Activity 的 onCreate 方法。
     * 初始化 RecyclerView 并加载媒体文件。
     *
     * @param savedInstanceState 保存的实例状态
     */
    @Override
    protected void onCreate(Bundle savedInstanceState)
    {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.media_browser);

        /* 初始化 RecyclerView 一个列表视图*/
        RecyclerView recyclerView = findViewById(R.id.recycler_view);
        recyclerView.setLayoutManager(new GridLayoutManager(this, 3));

        /* 加载媒体文件 */
        mediaFiles = getMediaFiles();
        MediaAdapter adapter = new MediaAdapter(mediaFiles, this::onMediaClicked);
        recyclerView.setAdapter(adapter);
    }

    /**
     * 获取媒体文件列表。
     *
     * @return 包含媒体文件的列表
     */
    private List<File> getMediaFiles()
    {
        /* 获取媒体文件的存储路径 */
        String mediaPath = TpFileManager.createVideoPath(this);
        File mediaDir = new File(mediaPath).getParentFile().getParentFile(); // 获取上级目录
        List<File> files = new ArrayList<>();
        searchMediaFiles(mediaDir, files);
        return files;
    }

    /**
     * 递归搜索指定目录中的媒体文件。
     *
     * @param dir   要搜索的目录
     * @param files 用于存储找到的媒体文件的列表
     */
    private void searchMediaFiles(File dir, List<File> files)
    {
        if (dir == null || !dir.isDirectory())
        {
            return;
        }

        File[] fileList = dir.listFiles();
        if (fileList == null) return;

        for (File file : fileList)
        {
            if (file.isDirectory())
            {
                searchMediaFiles(file, files);
            }
            else
            {
                String fileName = file.getName().toLowerCase();
                boolean isSupported = false;
                
                // 检查是否为支持的图片格式
                for (String format : SUPPORTED_IMAGE_FORMATS) {
                    if (fileName.endsWith(format)) {
                        isSupported = true;
                        break;
                    }
                }
                
                // 检查是否为支持的视频格式
                if (!isSupported) {
                    for (String format : SUPPORTED_VIDEO_FORMATS) {
                        if (fileName.endsWith(format)) {
                            isSupported = true;
                            break;
                        }
                    }
                }
                
                if (isSupported) {
                    files.add(file);
                }
            }
        }
    }

    /**
     * 当用户点击媒体文件时调用。
     * 根据文件类型启动相应的 Activity。
     *
     * @param file 被点击的媒体文件
     */
    private void onMediaClicked(File file)
    {
        String fileName = file.getName().toLowerCase();
        
        // 检查是否为视频文件
        for (String format : SUPPORTED_VIDEO_FORMATS) {
            if (fileName.endsWith(format)) {
                /* 启动TpVideoPlayerView播放器 Activity */
                TpVideoPlayerActivity.start(this, file.getAbsolutePath());
                return;
            }
        }
        
        // 检查是否为图片文件
        for (String format : SUPPORTED_IMAGE_FORMATS) {
            if (fileName.endsWith(format)) {
                /* 启动图片查看器 Activity */
                Intent intent = new Intent(this, ImageViewerActivity.class);
                intent.putExtra("imagePath", file.getAbsolutePath());
                startActivity(intent);
                return;
            }
        }
    }
}
