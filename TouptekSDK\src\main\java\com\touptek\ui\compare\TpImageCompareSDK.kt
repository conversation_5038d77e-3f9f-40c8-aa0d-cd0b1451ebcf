package com.touptek.ui.compare

import android.content.Context
import android.widget.Toast

/**
 * 图片对比功能统一SDK入口
 * 
 * 提供统一的API接口，根据图片数量自动选择合适的对比Activity
 * 支持2-4张图片的对比功能
 */
object TpImageCompareSDK {
    
    /**
     * 启动图片对比功能
     *
     * @param context 上下文
     * @param imagePaths 图片路径列表，支持2-4张图片
     */
    @JvmStatic
    fun startCompare(context: Context, imagePaths: List<String>) {
        when {
            imagePaths.isEmpty() -> {
                Toast.makeText(context, "请选择要对比的图片", Toast.LENGTH_SHORT).show()
            }
            imagePaths.size == 1 -> {
                Toast.makeText(context, "请选择至少2张图片进行对比", Toast.LENGTH_SHORT).show()
            }
            imagePaths.size == 2 -> {
                TpImageCompareActivity.start(context, imagePaths)
            }
            imagePaths.size == 3 -> {
                TpImageCompareTripleActivity.start(context, imagePaths)
            }
            imagePaths.size == 4 -> {
                TpImageCompareMultiActivity.start(context, imagePaths)
            }
            else -> {
                Toast.makeText(context, "最多支持4张图片对比", Toast.LENGTH_SHORT).show()
            }
        }
    }
    

}
