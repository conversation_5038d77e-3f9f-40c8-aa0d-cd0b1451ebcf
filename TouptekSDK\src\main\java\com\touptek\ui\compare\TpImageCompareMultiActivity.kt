package com.touptek.ui.compare

import android.content.Context
import android.content.Intent
import android.graphics.Matrix
import android.os.Bundle
import android.util.Log
import android.view.WindowManager
import android.widget.ImageButton
import android.widget.TextView
import android.widget.Toast
import androidx.appcompat.app.AppCompatActivity
import com.touptek.ui.TpImageView
import com.touptek.video.TpVideoSystem
import com.touptek.video.TpVideoConfig
import android.graphics.BitmapFactory
import java.io.File
import com.touptek.R

/**
 * 四图对比Activity - 支持2x2网格布局的四张图片对比
 * 支持全局同步、分组同步、独立操作三种模式
 */
class TpImageCompareMultiActivity : AppCompatActivity() {
    
    companion object {
        private const val TAG = "TpImageCompareMulti"
        private const val EXTRA_IMAGE_PATHS = "extra_image_paths"

        /**
         * 启动四图对比Activity
         */
        @JvmStatic
        fun start(context: Context, imagePaths: List<String>) {
            if (imagePaths.size != 4) {
                Toast.makeText(context, "需要提供4张图片路径", Toast.LENGTH_SHORT).show()
                return
            }
            val intent = Intent(context, TpImageCompareMultiActivity::class.java).apply {
                putStringArrayListExtra(EXTRA_IMAGE_PATHS, ArrayList(imagePaths))
            }
            context.startActivity(intent)
        }
    }
    
    // UI组件
    private lateinit var btnBack: ImageButton
    private lateinit var btnSyncMode: ImageButton
    private lateinit var btnReset: ImageButton
    private lateinit var btnSwap: ImageButton
    
    private lateinit var imageTopLeft: TpImageView
    private lateinit var imageTopRight: TpImageView
    private lateinit var imageBottomLeft: TpImageView
    private lateinit var imageBottomRight: TpImageView

    private lateinit var tvInfoTopLeft: TextView
    private lateinit var tvInfoTopRight: TextView
    private lateinit var tvInfoBottomLeft: TextView
    private lateinit var tvInfoBottomRight: TextView
    
    // 数据
    private var imagePaths = mutableListOf<String>()
    private var currentSyncMode = TpImageSyncEngine.SyncMode.GLOBAL
    
    // 同步引擎
    private lateinit var syncEngine: TpImageSyncEngine
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setupFullScreen()
        setContentView(R.layout.tp_image_compare_multi)

        initViews()
        getIntentData()
        setupSyncEngine()
        loadImages()
        updateImageInfo()
        setupClickListeners()
    }

    private fun setupFullScreen() {
        window.setFlags(
            WindowManager.LayoutParams.FLAG_FULLSCREEN,
            WindowManager.LayoutParams.FLAG_FULLSCREEN
        )
    }
    
    private fun initViews() {
        btnBack = findViewById(R.id.btn_back)
        btnSyncMode = findViewById(R.id.btn_sync_mode)
        btnReset = findViewById(R.id.btn_reset)
        btnSwap = findViewById(R.id.btn_swap)
        
        imageTopLeft = findViewById(R.id.image_top_left)
        imageTopRight = findViewById(R.id.image_top_right)
        imageBottomLeft = findViewById(R.id.image_bottom_left)
        imageBottomRight = findViewById(R.id.image_bottom_right)
        
        tvInfoTopLeft = findViewById(R.id.tv_info_top_left)
        tvInfoTopRight = findViewById(R.id.tv_info_top_right)
        tvInfoBottomLeft = findViewById(R.id.tv_info_bottom_left)
        tvInfoBottomRight = findViewById(R.id.tv_info_bottom_right)
        

    }
    
    private fun getIntentData() {
        imagePaths = intent.getStringArrayListExtra(EXTRA_IMAGE_PATHS)?.toMutableList() ?: mutableListOf()

        if (imagePaths.size != 4) {
            Toast.makeText(this, "图片路径无效", Toast.LENGTH_SHORT).show()
            finish()
            return
        }

        Log.d(TAG, "接收到${imagePaths.size}张图片路径，同步模式：$currentSyncMode")
    }
    
    private fun setupSyncEngine() {
        syncEngine = object : TpImageSyncEngine() {
            override fun getGroupTargets(sourceId: String): List<String> {
                // 四图对比的分组逻辑：上下两行分别为一组
                return when (sourceId) {
                    "top_left", "top_right" -> listOf("top_left", "top_right").filter { it != sourceId }
                    "bottom_left", "bottom_right" -> listOf("bottom_left", "bottom_right").filter { it != sourceId }
                    else -> emptyList()
                }
            }
        }

        syncEngine.apply {
            setSyncMode(currentSyncMode)
            setSyncEnabled(true)
            addImageView("top_left", imageTopLeft)
            addImageView("top_right", imageTopRight)
            addImageView("bottom_left", imageBottomLeft)
            addImageView("bottom_right", imageBottomRight)
        }
    }
    
    private fun loadImages() {
        val videoConfig = TpVideoConfig.createDefault4K()
        val videoSystem = TpVideoSystem(this, videoConfig)
        
        try {
            videoSystem.loadFullImage(imagePaths[0], imageTopLeft)
            videoSystem.loadFullImage(imagePaths[1], imageTopRight)
            videoSystem.loadFullImage(imagePaths[2], imageBottomLeft)
            videoSystem.loadFullImage(imagePaths[3], imageBottomRight)
            Log.d(TAG, "四张图片加载成功")
        } catch (e: Exception) {
            Log.e(TAG, "图片加载失败", e)
            Toast.makeText(this, "图片加载失败", Toast.LENGTH_SHORT).show()
        }
    }
    
    private fun updateImageInfo() {
        val infoViews = listOf(tvInfoTopLeft, tvInfoTopRight, tvInfoBottomLeft, tvInfoBottomRight)
        imagePaths.forEachIndexed { index, path ->
            val fileName = File(path).name
            val resolution = getImageResolution(path)
            val info = "$fileName ($resolution)"

            if (index < infoViews.size) {
                infoViews[index].text = info
            }
        }
    }
    
    private fun getImageResolution(imagePath: String): String {
        return try {
            val options = BitmapFactory.Options().apply {
                inJustDecodeBounds = true
            }
            BitmapFactory.decodeFile(imagePath, options)
            val width = options.outWidth
            val height = options.outHeight
            if (width > 0 && height > 0) {
                "${width}×${height}"
            } else {
                "未知分辨率"
            }
        } catch (e: Exception) {
            Log.e(TAG, "获取图片分辨率失败: $imagePath", e)
            "未知分辨率"
        }
    }
    
    private fun setupClickListeners() {
        btnBack.setOnClickListener { finish() }
        
        btnSyncMode.setOnClickListener {
            cycleSyncMode()
        }
        
        btnReset.setOnClickListener {
            loadImages()
            Toast.makeText(this, "已重置图片位置", Toast.LENGTH_SHORT).show()
        }
        
        btnSwap.setOnClickListener {
            swapImages()
        }
        
        updateSyncModeButtonState()
    }
    
    private fun cycleSyncMode() {
        currentSyncMode = when (currentSyncMode) {
            TpImageSyncEngine.SyncMode.GLOBAL -> TpImageSyncEngine.SyncMode.GROUP
            TpImageSyncEngine.SyncMode.GROUP -> TpImageSyncEngine.SyncMode.INDEPENDENT
            TpImageSyncEngine.SyncMode.INDEPENDENT -> TpImageSyncEngine.SyncMode.GLOBAL
        }
        
        syncEngine.setSyncMode(currentSyncMode)
        updateSyncModeButtonState()
        
        val modeText = when (currentSyncMode) {
            TpImageSyncEngine.SyncMode.GLOBAL -> "全局同步"
            TpImageSyncEngine.SyncMode.GROUP -> "分组同步（上下行）"
            TpImageSyncEngine.SyncMode.INDEPENDENT -> "独立操作"
        }
        Toast.makeText(this, "切换到：$modeText", Toast.LENGTH_SHORT).show()
    }
    
    private fun updateSyncModeButtonState() {
        btnSyncMode.alpha = when (currentSyncMode) {
            TpImageSyncEngine.SyncMode.GLOBAL -> 1.0f
            TpImageSyncEngine.SyncMode.GROUP -> 0.7f
            TpImageSyncEngine.SyncMode.INDEPENDENT -> 0.4f
        }
    }
    
    private fun swapImages() {
        // 顺时针旋转图片位置：左上->右上->右下->左下->左上
        val tempPath = imagePaths[0]
        imagePaths[0] = imagePaths[1]
        imagePaths[1] = imagePaths[3]
        imagePaths[3] = imagePaths[2]
        imagePaths[2] = tempPath
        
        // 重新加载图片
        loadImages()
        updateImageInfo()
        
        Toast.makeText(this, "已旋转图片位置", Toast.LENGTH_SHORT).show()
    }
    
    override fun onDestroy() {
        super.onDestroy()
        // 清理同步引擎资源
        imageTopLeft.setMatrixChangeListener(null)
        imageTopRight.setMatrixChangeListener(null)
        imageBottomLeft.setMatrixChangeListener(null)
        imageBottomRight.setMatrixChangeListener(null)
    }
}
