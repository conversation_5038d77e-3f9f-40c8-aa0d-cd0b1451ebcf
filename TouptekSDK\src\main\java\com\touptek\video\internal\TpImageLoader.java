package com.touptek.video.internal;

import android.content.Context;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.graphics.Matrix;
import android.os.Handler;
import android.os.Looper;
import android.util.Log;
import android.util.LruCache;
import android.widget.ImageView;

import com.bumptech.glide.Glide;
import com.bumptech.glide.load.engine.DiskCacheStrategy;

import org.beyka.tiffbitmapfactory.TiffBitmapFactory;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;


/**
 * TpImageLoader - BitmapFactory为核心的高性能图片加载器
 * <p>
 * 专为ToupTek摄像头应用设计的图片加载工具类，采用BitmapFactory为主要解码器，
 * 配合双层缓存机制（内存LRU + 磁盘缓存），仅对视频缩略图使用Glide。
 * </p>
 *
 * BitmapFactory核心加载策略：
 * <ul>
 *   <li><b>静态图片</b> - 所有JPEG、PNG、BMP、TIFF使用BitmapFactory解码</li>
 *   <li><b>双层缓存</b> - LRU内存缓存 + 磁盘缓存，实现即时加载</li>
 *   <li><b>视频缩略图</b> - 仅视频文件使用Glide提取缩略图</li>
 *   <li><b>智能采样</b> - 根据ImageView尺寸自动计算最优采样率</li>
 *   <li><b>请求管理</b> - 防止图片错乱的请求取消机制</li>
 *   <li><b>内存管理</b> - 自动释放不可见图片内存，避免OOM</li>
 * </ul>
 *
 * 主要功能：
 * <ul>
 *   <li>loadThumbnail() - 缩略图加载</li>
 *   <li>loadFullImage() - 高质量图片加载</li>
 *   <li>clearCache() - 缓存清理</li>
 *   <li>getCacheStats() - 缓存统计</li>
 *   <li>rotateBitmap() - 图片旋转工具</li>
 * </ul>
 *
 * <AUTHOR> Team
 * @version 1.0
 * @since 2025-01-17
 */
public class TpImageLoader
{
    private static final String TAG = "TpImageLoader";

    // ========== 线程管理相关变量 ==========

    /** 用于异步任务的线程池 */
    private static final ExecutorService executor = Executors.newFixedThreadPool(3);

    /** 主线程Handler，用于更新UI */
    private static final Handler mainHandler = new Handler(Looper.getMainLooper());

    // ========== 请求管理相关变量 ==========

    /** 为每个 ImageView 维护当前请求，防止图片错乱 */
    private static final ConcurrentHashMap<ImageView, Future<?>> activeRequests = new ConcurrentHashMap<>();

    // ========== 双层缓存机制相关变量 ==========

    /** 内存缓存最大容量：50MB */
    private static final int MEMORY_CACHE_SIZE = 50 * 1024 * 1024;

    /** LRU内存缓存实例 */
    private static final LruCache<String, Bitmap> memoryCache = new LruCache<String, Bitmap>(MEMORY_CACHE_SIZE)
    {
        @Override
        protected int sizeOf(String key, Bitmap bitmap)
        {
            return bitmap.getByteCount();
        }
    };

    /** 磁盘缓存目录 */
    private static File diskCacheDir;

    /** 磁盘缓存最大容量：200MB */
    private static final long MAX_DISK_CACHE_SIZE = 200 * 1024 * 1024;

    /** 磁盘缓存初始化状态标志 */
    private static boolean cacheInitialized = false;

    // ========== 文件格式相关常量 ==========

    /** 支持的视频文件扩展名数组 */
    private static final String[] VIDEO_EXTENSIONS = {"mp4", "avi", "mkv", "mov", "wmv", "flv", "3gp", "webm"};



    // ========== 公共API方法 ==========

    /**
     * 异步加载图片或视频缩略图到ImageView
     * <p>
     * 这是TpImageLoader的核心方法，采用BitmapFactory为主要解码策略，配合双层缓存机制
     * 实现高性能的图片和视频缩略图加载。该方法会根据ImageView的实际尺寸进行智能采样，
     * 有效控制内存使用并提供流畅的用户体验。
     * </p>
     *
     * <p>核心加载策略：</p>
     * <ul>
     *   <li><b>静态图片</b>：JPEG、PNG、BMP、TIFF格式使用BitmapFactory解码</li>
     *   <li><b>视频缩略图</b>：视频文件使用Glide库提取缩略图</li>
     *   <li><b>双层缓存</b>：LRU内存缓存 + 磁盘缓存，实现即时加载</li>
     *   <li><b>智能采样</b>：根据ImageView尺寸自动计算最优采样率</li>
     *   <li><b>请求管理</b>：防止图片错乱的请求取消机制</li>
     *   <li><b>内存管理</b>：自动释放不可见图片内存，避免OOM</li>
     * </ul>
     *
     * <p>适用场景：</p>
     * <ul>
     *   <li>列表和网格视图中的图片显示</li>
     *   <li>文件浏览器的缩略图预览</li>
     *   <li>视频文件的封面图显示</li>
     *   <li>需要快速加载的图片场景</li>
     * </ul>
     *
     * <p>性能特点：</p>
     * <ul>
     *   <li>异步加载，不阻塞UI线程</li>
     *   <li>内存缓存命中时即时显示</li>
     *   <li>磁盘缓存命中时快速加载</li>
     *   <li>智能采样减少内存占用</li>
     * </ul>
     *
     * @param path 图片或视频文件的完整路径，不能为null
     * @param imageView 目标ImageView控件，不能为null
     * @throws IllegalArgumentException 当path或imageView为null时
     */
    public static void loadThumbnail(String path, ImageView imageView)
    {
        if (path == null || imageView == null)
        {
            Log.e(TAG, "Invalid parameters: path or imageView is null");
            return;
        }

        // 初始化缓存系统
        Context context = imageView.getContext();
        if (context != null)
        {
            initializeDiskCache(context);
        }

        // ========== 请求管理机制 ==========

        // 取消旧请求，防止图片错乱
        Future<?> oldRequest = activeRequests.get(imageView);
        if (oldRequest != null && !oldRequest.isDone())
        {
            oldRequest.cancel(true);
        }

        // 检查是否需要清空ImageView
        String currentTag = (String) imageView.getTag();
        boolean needsClear = !path.equals(currentTag);

        // 设置新标签
        imageView.setTag(path);

        // 只有在路径改变时才清空，避免不必要的闪烁
        if (needsClear) {
            imageView.setImageBitmap(null);
        }

        // 获取ImageView尺寸并生成缓存键
        int[] dimensions = getImageViewDimensions(imageView);
        String cacheKey = generateCacheKey(path, dimensions[0], dimensions[1]);

        // 优先检查内存缓存
        Bitmap cachedBitmap = memoryCache.get(cacheKey);
        if (cachedBitmap != null && !cachedBitmap.isRecycled())
        {
            imageView.setImageBitmap(cachedBitmap);
            Log.d(TAG, "Loaded from memory cache: " + path + " (" + dimensions[0] + "x" + dimensions[1] + ")");
            return;
        }

        // 提交异步加载任务
        Future<?> newRequest = executor.submit(() ->
        {
            try
            {
                // 检查线程是否被中断
                if (Thread.currentThread().isInterrupted())
                {
                    return;
                }

                // 验证文件存在性
                File file = new File(path);
                if (!file.exists())
                {
                    Log.w(TAG, "File not found: " + path);
                    return;
                }

                Bitmap bitmap = null;

                // 检查磁盘缓存
                bitmap = loadFromDiskCache(cacheKey);
                if (bitmap != null)
                {
                    Log.d(TAG, "Loaded from disk cache: " + path + " (" + dimensions[0] + "x" + dimensions[1] + ")");
                }
                else
                {
                    // 根据文件类型选择最优加载策略
                    if (isVideoFile(path))
                    {
                        // 视频文件使用Glide提取缩略图
                        bitmap = loadVideoThumbnailWithGlide(context, path, dimensions[0], dimensions[1]);
                        Log.d(TAG, "Loaded video thumbnail with Glide: " + path);
                    }
                    else
                    {
                        // 静态图片使用BitmapFactory智能采样
                        bitmap = decodeSampledBitmapFromFile(path, dimensions[0], dimensions[1]);
                        Log.d(TAG, "Loaded image with BitmapFactory: " + path);
                    }

                    // 异步保存到磁盘缓存
                    if (bitmap != null)
                    {
                        saveToDiskCache(cacheKey, bitmap);
                    }
                }

                // 再次检查线程中断状态
                if (Thread.currentThread().isInterrupted())
                {
                    if (bitmap != null && !bitmap.isRecycled())
                    {
                        bitmap.recycle();
                    }
                    return;
                }

                // 切换到UI线程设置图片
                final Bitmap finalBitmap = bitmap;
                mainHandler.post(() ->
                {
                    // 检查ImageView标签匹配，防止图片错乱
                    if (path.equals(imageView.getTag()) && finalBitmap != null)
                    {
                        // 添加到内存缓存
                        memoryCache.put(cacheKey, finalBitmap);
                        // 设置到ImageView
                        imageView.setImageBitmap(finalBitmap);
                        Log.d(TAG, "Image loading completed: " + path + " (" + finalBitmap.getWidth() + "x" + finalBitmap.getHeight() + ")");
                    }
                    else if (finalBitmap != null && !finalBitmap.isRecycled())
                    {
                        // ImageView已被重用，延迟回收bitmap避免内存泄漏
                        executor.execute(() ->
                        {
                            try
                            {
                                Thread.sleep(100);
                            }
                            catch (InterruptedException ignored)
                            {
                                // 忽略中断异常
                            }
                            if (!finalBitmap.isRecycled())
                            {
                                finalBitmap.recycle();
                            }
                        });
                    }
                });

            }
            catch (Exception e)
            {
                // 过滤正常的中断异常，只记录真实错误
                if (!(e instanceof InterruptedException || Thread.currentThread().isInterrupted()))
                {
                    Log.e(TAG, "Error occurred while loading image: " + path, e);
                }
            }
            finally
            {
                // 清理请求记录
                activeRequests.remove(imageView);
            }
        });

        // 记录新的异步请求
        activeRequests.put(imageView, newRequest);
    }

    /**
     * 高质量加载静态图片到ImageView（原始尺寸，无压缩）
     * <p>
     * 该方法专门用于静态图片的高质量显示，加载图片的原始尺寸而不进行采样压缩。
     * 适用于图片详情查看、放大显示等需要高质量图像的场景。
     * </p>
     *
     * <p>功能特点：</p>
     * <ul>
     *   <li>专门用于静态图片的高质量显示</li>
     *   <li>加载原始尺寸，不进行采样压缩</li>
     *   <li>支持JPEG、PNG、BMP、TIFF等格式</li>
     *   <li>使用双层缓存机制提升性能</li>
     *   <li>异步加载，不阻塞UI线程</li>
     *   <li>防止图片错乱的标签机制</li>
     * </ul>
     *
     * <p>注意事项：</p>
     * <ul>
     *   <li>不支持视频文件，视频缩略图请使用loadThumbnail()方法</li>
     *   <li>原始尺寸加载可能占用较多内存，请注意内存管理</li>
     *   <li>大图片加载时间较长，建议在详情页面使用</li>
     * </ul>
     *
     * @param imagePath 图片文件的完整路径，不能为null
     * @param imageView 目标ImageView控件，不能为null
     * @return 如果成功启动加载任务返回true，参数无效或文件不存在返回false
     * @throws SecurityException 当文件访问权限不足时
     */
    public static boolean loadFullImage(String imagePath, ImageView imageView)
    {
        if (imagePath == null || imageView == null)
        {
            Log.e(TAG, "Invalid parameters: path or imageView is null");
            return false;
        }

        // 初始化缓存系统
        Context context = imageView.getContext();
        if (context != null)
        {
            initializeDiskCache(context);
        }

        try
        {
            // 检查文件是否存在
            File imageFile = new File(imagePath);
            if (!imageFile.exists() || !imageFile.isFile())
            {
                Log.e(TAG, "Image file not found: " + imagePath);
                return false;
            }

            // 设置ImageView标签，防止图片错乱
            imageView.setTag(imagePath);

            // 高质量加载使用固定的"original"标识，不依赖ImageView尺寸
            String cacheKey = generateCacheKey(imagePath, -1, -1) + "_original";

            // 先检查内存缓存
            Bitmap cachedBitmap = memoryCache.get(cacheKey);
            if (cachedBitmap != null && !cachedBitmap.isRecycled())
            {
                if (imagePath.equals(imageView.getTag()))
                {
                    imageView.setImageBitmap(cachedBitmap);
                    Log.d(TAG, "Loaded high-quality image from memory cache: " + imagePath + " (original size)");
                    return true;
                }
            }

            // 异步加载
            executor.execute(() ->
            {
                try
                {
                    Bitmap bitmap = null;

                    // 先检查磁盘缓存
                    bitmap = loadFromDiskCache(cacheKey);
                    if (bitmap != null)
                    {
                        Log.d(TAG, "Loaded high-quality image from disk cache: " + imagePath + " (original size)");
                    }
                    else
                    {
                        // 只处理静态图片，视频文件应该使用loadThumbnail()
                        if (isVideoFile(imagePath))
                        {
                            Log.w(TAG, "loadFullImage does not support video files, please use loadThumbnail(): " + imagePath);
                            return; // 直接返回，不处理视频文件
                        }

                        // 静态图片使用decodeSampledBitmapFromFile（内部会自动处理TIFF格式）
                        bitmap = decodeSampledBitmapFromFile(imagePath, Integer.MAX_VALUE, Integer.MAX_VALUE);
                        Log.d(TAG, "Loaded original size static image: " + imagePath);

                        // 保存到磁盘缓存
                        if (bitmap != null) {
                            saveToDiskCache(cacheKey, bitmap);
                        }
                    }

                    final Bitmap finalBitmap = bitmap;
                    mainHandler.post(() -> {
                        // 检查ImageView标签是否匹配，防止图片错乱
                        if (imagePath.equals(imageView.getTag()) && finalBitmap != null) {
                            // 添加到内存缓存
                            memoryCache.put(cacheKey, finalBitmap);
                            // 设置到ImageView
                            imageView.setImageBitmap(finalBitmap);
                            Log.d(TAG, "高质量图片加载完成: " + imagePath +
                                    " (" + finalBitmap.getWidth() + "x" + finalBitmap.getHeight() + ")");
                        } else if (finalBitmap != null && !finalBitmap.isRecycled()) {
                            // 如果ImageView已经被重用，延迟回收bitmap
                            executor.execute(() -> {
                                try { Thread.sleep(100); } catch (InterruptedException ignored) {}
                                if (!finalBitmap.isRecycled()) {
                                    finalBitmap.recycle();
                                }
                            });
                        }
                    });

                } catch (Exception e) {
                    Log.e(TAG, "加载高质量图片时发生错误: " + imagePath, e);
                }
            });

            return true;
        } catch (Exception e) {
            Log.e(TAG, "Error loading image: " + e.getMessage(), e);
            return false;
        }
    }

    /**
     * 旋转图片到指定角度
     * <p>
     * 该工具方法使用Matrix变换将Bitmap旋转指定的角度，常用于图片方向校正。
     * 创建新的Bitmap对象，原始Bitmap保持不变。
     * </p>
     *
     * <p>功能特点：</p>
     * <ul>
     *   <li>支持任意角度旋转（0-360度）</li>
     *   <li>使用Matrix变换确保高质量旋转</li>
     *   <li>自动处理尺寸变化</li>
     *   <li>原始Bitmap保持不变</li>
     * </ul>
     *
     * <p>常用场景：</p>
     * <ul>
     *   <li>EXIF方向信息校正</li>
     *   <li>用户手动旋转图片</li>
     *   <li>相机拍摄方向调整</li>
     * </ul>
     *
     * @param bitmap 原始Bitmap对象，不能为null
     * @param degrees 旋转角度（度），正值为顺时针旋转
     * @return 旋转后的新Bitmap对象，输入为null时返回null
     */
    public static Bitmap rotateBitmap(Bitmap bitmap, float degrees)
    {
        if (bitmap == null)
        {
            return null;
        }

        Matrix matrix = new Matrix();
        matrix.postRotate(degrees);
        return Bitmap.createBitmap(bitmap, 0, 0, bitmap.getWidth(), bitmap.getHeight(), matrix, true);
    }

    // ========== 缓存管理核心方法 ==========

    /**
     * 初始化磁盘缓存系统
     * <p>
     * 该方法负责创建和配置磁盘缓存目录，采用应用专用内部缓存策略以确保稳定性。
     * 缓存目录位于应用专用的内部存储空间，不受外部存储设备插拔影响。
     * </p>
     *
     * <p>缓存策略特点：</p>
     * <ul>
     *   <li>使用应用专用内部缓存目录：/storage/emulated/0/Android/data/包名/cache/</li>
     *   <li>避免外部存储（如U盘）路径，确保稳定性</li>
     *   <li>不受外部存储设备插拔影响</li>
     *   <li>支持并发访问的线程安全初始化</li>
     * </ul>
     *
     * @param context Android上下文对象，用于获取应用缓存目录
     * @throws SecurityException 当缓存目录创建失败或无写入权限时
     */
    private static synchronized void initializeDiskCache(Context context)
    {
        if (cacheInitialized)
        {
            return;
        }

        try
        {
            // 确保使用应用专用内部缓存目录
            File appCacheDir = context.getCacheDir();
            if (appCacheDir == null)
            {
                Log.e(TAG, "Failed to get application cache directory");
                return;
            }

            // 创建TpImageLoader专用缓存子目录
            diskCacheDir = new File(appCacheDir, "tp_image_cache");
            if (!diskCacheDir.exists())
            {
                boolean created = diskCacheDir.mkdirs();
                if (!created)
                {
                    Log.e(TAG, "Failed to create cache directory: " + diskCacheDir.getAbsolutePath());
                    return;
                }
            }

            // 验证目录可写权限
            if (!diskCacheDir.canWrite())
            {
                Log.e(TAG, "Cache directory is not writable: " + diskCacheDir.getAbsolutePath());
                return;
            }

            cacheInitialized = true;
            Log.d(TAG, "Disk cache initialized successfully: " + diskCacheDir.getAbsolutePath());

        }
        catch (Exception e)
        {
            Log.e(TAG, "Failed to initialize disk cache", e);
            cacheInitialized = false;
            diskCacheDir = null;
        }
    }

    /**
     * 生成缓存键
     * <p>
     * 根据文件路径和目标尺寸生成唯一的缓存键，用于标识特定尺寸的图片缓存。
     * 优先使用MD5哈希算法确保键的唯一性和固定长度，如果MD5不可用则降级使用hashCode。
     * </p>
     *
     * @param filePath 图片文件的完整路径
     * @param width 目标宽度（像素）
     * @param height 目标高度（像素）
     * @return 32位十六进制MD5哈希字符串，或整数哈希值的字符串表示
     */
    private static String generateCacheKey(String filePath, int width, int height)
    {
        try
        {
            String input = filePath + "_" + width + "x" + height;
            MessageDigest md = MessageDigest.getInstance("MD5");
            byte[] digest = md.digest(input.getBytes());
            StringBuilder sb = new StringBuilder();
            for (byte b : digest)
            {
                sb.append(String.format("%02x", b));
            }
            return sb.toString();
        }
        catch (NoSuchAlgorithmException e)
        {
            // 如果MD5不可用，使用简单的hash作为降级方案
            return String.valueOf((filePath + "_" + width + "x" + height).hashCode());
        }
    }

    /**
     * 检查文件是否为支持的视频格式
     * <p>
     * 通过文件扩展名判断是否为视频文件，支持常见的视频格式包括MP4、AVI、MKV等。
     * 扩展名比较不区分大小写。
     * </p>
     *
     * @param filePath 要检查的文件路径
     * @return 如果是支持的视频格式返回true，否则返回false
     */
    private static boolean isVideoFile(String filePath)
    {
        String extension = getFileExtension(filePath).toLowerCase();
        for (String videoExt : VIDEO_EXTENSIONS)
        {
            if (videoExt.equals(extension))
            {
                return true;
            }
        }
        return false;
    }

    /**
     * 清除所有缓存数据
     * <p>
     * 该方法会清除内存缓存和磁盘缓存中的所有数据，包括JPEG缓存文件和临时文件。
     * 磁盘清理操作在后台线程中异步执行，不会阻塞调用线程。
     * </p>
     *
     * <p>清理范围包括：</p>
     * <ul>
     *   <li>LRU内存缓存中的所有Bitmap对象</li>
     *   <li>磁盘缓存目录中的.jpg文件</li>
     *   <li>磁盘缓存目录中的.tmp临时文件</li>
     * </ul>
     */
    public static void clearCache()
    {
        // 立即清除内存缓存
        memoryCache.evictAll();

        // 异步清除磁盘缓存
        executor.execute(() ->
        {
            try
            {
                if (cacheInitialized && diskCacheDir != null)
                {
                    File[] allFiles = diskCacheDir.listFiles();
                    if (allFiles != null)
                    {
                        int deletedCount = 0;
                        long deletedSize = 0;

                        for (File file : allFiles)
                        {
                            String name = file.getName();
                            // 清除缓存相关文件：.jpg, .tmp
                            if (name.endsWith(".jpg") || name.endsWith(".tmp"))
                            {
                                deletedSize += file.length();
                                if (file.delete())
                                {
                                    deletedCount++;
                                }
                            }
                        }

                        Log.d(TAG, "Disk cache cleared: " + deletedCount + " files, " +
                              (deletedSize / 1024 / 1024) + "MB");
                    }
                }
            }
            catch (Exception e)
            {
                Log.e(TAG, "Error occurred while clearing disk cache", e);
            }
        });
    }

    /**
     * 获取缓存统计信息
     * <p>
     * 提供详细的缓存状态信息，用于监控和调试缓存系统的使用情况。
     * 该方法会统计内存缓存和磁盘缓存的使用情况，包括文件数量、占用空间和使用率。
     * </p>
     *
     * <p>统计信息包括：</p>
     * <ul>
     *   <li>内存缓存：条目数量、占用内存、使用率百分比</li>
     *   <li>磁盘缓存：JPEG文件数量、临时文件数量、总占用空间、使用率百分比</li>
     *   <li>缓存目录：磁盘缓存的完整路径</li>
     * </ul>
     *
     * @return 格式化的缓存统计信息字符串
     */
    public static String getCacheStats()
    {
        long memorySize = 0;
        int memoryCount = 0;

        // 线程安全地计算内存缓存大小
        synchronized (memoryCache)
        {
            memorySize = memoryCache.size();
            memoryCount = memoryCache.snapshot().size();
        }

        // 计算磁盘缓存统计信息
        long diskSize = 0;
        int jpegCount = 0;
        int tempCount = 0;
        String cacheDir = "Not initialized";

        if (cacheInitialized && diskCacheDir != null)
        {
            cacheDir = diskCacheDir.getAbsolutePath();
            File[] allFiles = diskCacheDir.listFiles();
            if (allFiles != null)
            {
                for (File file : allFiles)
                {
                    String name = file.getName();
                    long size = file.length();
                    diskSize += size;

                    if (name.endsWith(".jpg"))
                    {
                        jpegCount++;
                    }
                    else if (name.endsWith(".tmp"))
                    {
                        tempCount++;
                    }
                }
            }
        }

        // 构建统计信息字符串
        StringBuilder stats = new StringBuilder();
        stats.append("TpImageLoader Cache Statistics:\n");
        stats.append(String.format("Memory: %d items/%.1fMB (%.1f%%)\n",
                memoryCount, memorySize / 1024.0 / 1024.0,
                (memorySize * 100.0) / MEMORY_CACHE_SIZE));
        stats.append(String.format("Disk: %d items/%.1fMB (%.1f%%) - JPEG:%d, Temp:%d\n",
                jpegCount, diskSize / 1024.0 / 1024.0,
                (diskSize * 100.0) / MAX_DISK_CACHE_SIZE,
                jpegCount, tempCount));
        stats.append("Cache Directory: ").append(cacheDir);

        return stats.toString();
    }



    /**
     * 获取ImageView的实际显示尺寸
     * <p>
     * 该方法采用多级降级策略获取ImageView的尺寸，用于计算最优的图片采样率。
     * 优先使用已测量的实际尺寸，其次使用布局参数中的设计尺寸，最后使用合理的默认尺寸。
     * </p>
     *
     * <p>尺寸获取策略：</p>
     * <ol>
     *   <li>优先使用ImageView的实际测量尺寸（getWidth/getHeight）</li>
     *   <li>如果未测量，从LayoutParams获取设计尺寸</li>
     *   <li>最后使用300x300的默认尺寸（适合大多数缩略图场景）</li>
     * </ol>
     *
     * <p>默认尺寸说明：</p>
     * <ul>
     *   <li>300x300像素适合大多数列表和网格缩略图</li>
     *   <li>相比1920x1080大幅减少内存占用</li>
     *   <li>在高密度屏幕上仍能提供清晰显示</li>
     * </ul>
     *
     * @param imageView 目标ImageView控件，不能为null
     * @return 包含宽度和高度的整数数组 [width, height]，保证都大于0
     */
    private static int[] getImageViewDimensions(ImageView imageView)
    {
        int width = imageView.getWidth();
        int height = imageView.getHeight();

        // 如果ImageView还未完成测量，尝试从布局参数获取
        if (width <= 0 || height <= 0)
        {
            android.view.ViewGroup.LayoutParams params = imageView.getLayoutParams();
            if (params != null)
            {
                if (params.width > 0)
                {
                    width = params.width;
                }
                if (params.height > 0)
                {
                    height = params.height;
                }
            }
        }

        // 使用合理的默认尺寸作为最后降级方案
        if (width <= 0 || height <= 0)
        {
            width = 300;   // 适合缩略图的默认宽度
            height = 300;  // 适合缩略图的默认高度
        }

        return new int[]{width, height};
    }

    /**
     * 从磁盘缓存加载图片
     * <p>
     * 该方法负责从磁盘缓存中读取已缓存的图片数据，采用JPEG格式存储以获得最佳的
     * 解码性能和文件大小平衡。包含完整的文件验证和错误恢复机制。
     * </p>
     *
     * <p>优化策略：</p>
     * <ul>
     *   <li>使用JPEG格式缓存文件，Android平台解码性能最优</li>
     *   <li>文件扩展名使用.jpg，明确标识格式便于管理</li>
     *   <li>文件完整性检查，自动清理损坏的缓存文件</li>
     *   <li>异常安全，确保不会因缓存问题影响主流程</li>
     * </ul>
     *
     * <p>错误处理：</p>
     * <ul>
     *   <li>自动检测并删除损坏的缓存文件</li>
     *   <li>记录详细的错误日志便于调试</li>
     *   <li>失败时返回null，不影响后续加载流程</li>
     * </ul>
     *
     * @param cacheKey 缓存键，由generateCacheKey方法生成
     * @return 成功时返回解码的Bitmap对象，失败时返回null
     */
    private static Bitmap loadFromDiskCache(String cacheKey)
    {
        if (!cacheInitialized || diskCacheDir == null)
        {
            return null;
        }

        try
        {
            // 构建JPEG格式的缓存文件路径
            File cacheFile = new File(diskCacheDir, cacheKey + ".jpg");
            if (cacheFile.exists() && cacheFile.length() > 0)
            {
                // 尝试解码并验证文件完整性
                Bitmap bitmap = BitmapFactory.decodeFile(cacheFile.getAbsolutePath());
                if (bitmap != null)
                {
                    return bitmap;
                }
                else
                {
                    // 文件损坏，自动清理
                    Log.w(TAG, "Corrupted cache file detected and removed: " + cacheFile.getName());
                    cacheFile.delete();
                }
            }
        }
        catch (Exception e)
        {
            Log.e(TAG, "Failed to load from disk cache: " + cacheKey, e);
        }
        return null;
    }

    /**
     * 保存图片到磁盘缓存
     * <p>
     * 该方法负责将Bitmap对象异步保存到磁盘缓存，采用JPEG格式和原子写入策略
     * 确保缓存文件的完整性和一致性。使用智能压缩策略平衡文件大小和图像质量。
     * </p>
     *
     * <p>优化策略：</p>
     * <ul>
     *   <li>JPEG格式：Android平台编码/解码性能最优</li>
     *   <li>压缩质量80%：平衡文件大小和图像质量</li>
     *   <li>原子写入：先写临时文件，成功后重命名，避免损坏</li>
     *   <li>异步执行：不阻塞主线程，提升用户体验</li>
     *   <li>智能预处理：自动转换不兼容的Bitmap配置</li>
     * </ul>
     *
     * <p>安全机制：</p>
     * <ul>
     *   <li>参数验证：确保缓存系统已初始化且Bitmap有效</li>
     *   <li>原子操作：避免写入过程中的文件损坏</li>
     *   <li>异常处理：写入失败时自动清理临时文件</li>
     *   <li>内存管理：及时回收临时创建的Bitmap对象</li>
     * </ul>
     *
     * @param cacheKey 缓存键，用于生成缓存文件名
     * @param bitmap 要保存的Bitmap对象，不能为null或已回收
     */
    private static void saveToDiskCache(String cacheKey, Bitmap bitmap)
    {
        if (!cacheInitialized || diskCacheDir == null || bitmap == null || bitmap.isRecycled())
        {
            return;
        }

        // 异步执行保存操作，避免阻塞调用线程
        executor.execute(() ->
        {
            FileOutputStream fos = null;
            File tempFile = null;
            File finalFile = null;

            try
            {
                // 构建JPEG格式的缓存文件路径
                finalFile = new File(diskCacheDir, cacheKey + ".jpg");
                tempFile = new File(diskCacheDir, cacheKey + ".tmp");

                // 原子写入策略：先写临时文件
                fos = new FileOutputStream(tempFile);

                // JPEG压缩：80%质量平衡文件大小和图像质量
                boolean success = bitmap.compress(Bitmap.CompressFormat.JPEG, 80, fos);
                fos.flush();
                fos.close();
                fos = null;

                if (success && tempFile.length() > 0)
                {
                    // 原子操作：重命名临时文件为最终文件
                    if (finalFile.exists())
                    {
                        finalFile.delete();
                    }

                    boolean renamed = tempFile.renameTo(finalFile);
                    if (renamed)
                    {
                        // 检查缓存大小，如果超过限制则清理
                        checkAndCleanDiskCache();
                    }
                    else
                    {
                        Log.w(TAG, "Failed to rename cache file: " + cacheKey);
                        tempFile.delete();
                    }
                }
                else
                {
                    Log.w(TAG, "JPEG compression failed: " + cacheKey);
                    tempFile.delete();
                }

            } catch (Exception e) {
                Log.e(TAG, "保存到磁盘缓存失败: " + cacheKey, e);
                // 清理临时文件
                if (tempFile != null && tempFile.exists()) {
                    tempFile.delete();
                }
            } finally {
                if (fos != null) {
                    try {
                        fos.close();
                    } catch (IOException ignored) {}
                }
            }
        });
    }

    /**
     * 检查并清理磁盘缓存
     * <p>
     * 清理策略：
     * - 支持JPEG格式(.jpg扩展名)
     * - 按最后访问时间排序，删除最旧的文件
     * - 清理到80%容量，避免频繁清理
     * - 清理临时文件(.tmp)
     * </p>
     */
    private static void checkAndCleanDiskCache() {
        if (!cacheInitialized || diskCacheDir == null) return;

        try {
            File[] allFiles = diskCacheDir.listFiles();
            if (allFiles == null) return;

            // 过滤出缓存文件（.jpg）和临时文件（.tmp）
            java.util.List<File> cacheFiles = new java.util.ArrayList<>();
            java.util.List<File> tempFiles = new java.util.ArrayList<>();

            for (File file : allFiles) {
                String name = file.getName();
                if (name.endsWith(".jpg")) {
                    cacheFiles.add(file);
                } else if (name.endsWith(".tmp")) {
                    tempFiles.add(file);
                }
            }

            // 清理所有临时文件
            for (File tempFile : tempFiles) {
                tempFile.delete();
            }

            // 计算缓存文件总大小
            long totalSize = 0;
            for (File file : cacheFiles) {
                totalSize += file.length();
            }

            if (totalSize > MAX_DISK_CACHE_SIZE) {
                // 按最后修改时间排序，删除最旧的文件
                cacheFiles.sort((f1, f2) -> Long.compare(f1.lastModified(), f2.lastModified()));

                long targetSize = (long)(MAX_DISK_CACHE_SIZE * 0.8); // 清理到80%
                int deletedCount = 0;

                for (File file : cacheFiles) {
                    if (totalSize <= targetSize) break;

                    long fileSize = file.length();
                    if (file.delete()) {
                        totalSize -= fileSize;
                        deletedCount++;
                    }
                }

                Log.d(TAG, "磁盘缓存清理完成: 删除" + deletedCount + "个文件，" +
                      "当前大小: " + (totalSize / 1024 / 1024) + "MB");
            }
        } catch (Exception e) {
            Log.e(TAG, "清理磁盘缓存失败", e);
        }
    }

    /**
     * 使用Glide加载视频缩略图
     * <p>
     * 该方法专门用于从视频文件中提取缩略图，利用Glide库的强大视频处理能力
     * 实现高效的视频帧提取和缩放。支持多种视频格式，并提供完整的缓存机制。
     * </p>
     *
     * <p>功能特点：</p>
     * <ul>
     *   <li>支持多种视频格式：MP4、AVI、MKV、MOV等</li>
     *   <li>智能帧提取：自动选择最佳帧作为缩略图</li>
     *   <li>尺寸控制：精确控制输出缩略图的尺寸</li>
     *   <li>缓存优化：启用Glide的磁盘缓存机制</li>
     *   <li>同步获取：适合在后台线程中调用</li>
     * </ul>
     *
     * <p>注意事项：</p>
     * <ul>
     *   <li>该方法会阻塞调用线程，应在后台线程中使用</li>
     *   <li>大视频文件的缩略图提取可能耗时较长</li>
     *   <li>依赖Glide库，确保已正确集成</li>
     * </ul>
     *
     * @param context Android上下文对象，用于Glide初始化
     * @param videoPath 视频文件的完整路径
     * @param width 目标缩略图宽度（像素）
     * @param height 目标缩略图高度（像素）
     * @return 成功时返回视频缩略图Bitmap，失败时返回null
     */
    private static Bitmap loadVideoThumbnailWithGlide(Context context, String videoPath, int width, int height)
    {
        try
        {
            // 使用Glide同步方式提取视频缩略图
            return Glide.with(context)
                    .asBitmap()
                    .load(videoPath)
                    .override(width, height)
                    .diskCacheStrategy(DiskCacheStrategy.ALL)
                    .submit()
                    .get();
        }
        catch (Exception e)
        {
            Log.e(TAG, "Failed to load video thumbnail with Glide: " + videoPath, e);
            return null;
        }
    }

    /**
     * 解码图片文件，使用内存优化的智能采样方式
     * <p>
     * 该方法是TpImageLoader的核心图片解码器，采用两阶段解码策略实现内存优化。
     * 首先获取图片尺寸信息，然后根据目标尺寸计算最优采样率，最后进行实际解码。
     * 支持多种图片格式，并对TIFF格式提供专门的处理逻辑。
     * </p>
     *
     * <p>解码策略：</p>
     * <ul>
     *   <li>两阶段解码：先获取尺寸，再计算采样率，最后解码</li>
     *   <li>智能采样：根据目标尺寸自动计算最优的inSampleSize</li>
     *   <li>格式适配：TIFF格式使用专门的解码器</li>
     *   <li>内存优化：采样率确保输出图片不超过目标尺寸</li>
     * </ul>
     *
     * <p>支持格式：</p>
     * <ul>
     *   <li>JPEG：Android原生支持，性能最优</li>
     *   <li>PNG：支持透明通道</li>
     *   <li>BMP：Windows位图格式</li>
     *   <li>TIFF：使用TiffBitmapFactory专门处理</li>
     * </ul>
     *
     * @param imagePath 图片文件的完整路径，不能为null
     * @param reqWidth 请求的目标宽度（像素），用于计算采样率
     * @param reqHeight 请求的目标高度（像素），用于计算采样率
     * @return 解码后的Bitmap对象，失败时返回null
     */
    public static Bitmap decodeSampledBitmapFromFile(String imagePath, int reqWidth, int reqHeight)
    {
        try
        {
            // 自动检测图片格式
            String extension = getFileExtension(imagePath).toLowerCase();

            // TIFF格式使用专门的解码器
            if ("tiff".equals(extension) || "tif".equals(extension))
            {
                return decodeTiffWithSampling(imagePath, reqWidth, reqHeight);
            }

            // 其他格式使用BitmapFactory两阶段解码
            final BitmapFactory.Options options = new BitmapFactory.Options();
            options.inJustDecodeBounds = true;
            BitmapFactory.decodeFile(imagePath, options);

            // 计算最优采样率
            options.inSampleSize = calculateInSampleSize(options, reqWidth, reqHeight);

            // 实际解码图片
            options.inJustDecodeBounds = false;
            return BitmapFactory.decodeFile(imagePath, options);
        }
        catch (Exception e)
        {
            Log.e(TAG, "Error occurred while decoding bitmap: " + e.getMessage(), e);
            return null;
        }
    }

    /**
     * TIFF格式的智能采样解码
     *
     * @param imagePath TIFF图片文件路径
     * @param reqWidth 请求的宽度
     * @param reqHeight 请求的高度
     * @return 解码后的Bitmap对象，解码失败时返回null
     */
    private static Bitmap decodeTiffWithSampling(String imagePath, int reqWidth, int reqHeight) {
        try {
            // 先获取TIFF图片尺寸
            TiffBitmapFactory.Options options = new TiffBitmapFactory.Options();
            options.inJustDecodeBounds = true;
            TiffBitmapFactory.decodeFile(new File(imagePath), options);

            // 计算采样率（使用统一的算法）
            BitmapFactory.Options bfOptions = new BitmapFactory.Options();
            bfOptions.outWidth = options.outWidth;
            bfOptions.outHeight = options.outHeight;
            options.inSampleSize = calculateInSampleSize(bfOptions, reqWidth, reqHeight);

            // 实际解码
            options.inJustDecodeBounds = false;
            return TiffBitmapFactory.decodeFile(new File(imagePath), options);
        } catch (Exception e) {
            Log.e(TAG, "Error decoding TIFF: " + e.getMessage(), e);
            // TIFF解码失败时降级到BitmapFactory尝试
            Log.w(TAG, "TIFF decoding failed, trying BitmapFactory: " + imagePath);
            try {
                final BitmapFactory.Options options = new BitmapFactory.Options();
                options.inJustDecodeBounds = true;
                BitmapFactory.decodeFile(imagePath, options);

                options.inSampleSize = calculateInSampleSize(options, reqWidth, reqHeight);
                options.inJustDecodeBounds = false;
                return BitmapFactory.decodeFile(imagePath, options);
            } catch (Exception fallbackException) {
                Log.e(TAG, "BitmapFactory fallback also failed: " + fallbackException.getMessage(), fallbackException);
                return null;
            }
        }
    }



    /**
     * 计算适当的采样率
     *
     * @param options 包含原始图片尺寸的选项
     * @param reqWidth 请求的宽度
     * @param reqHeight 请求的高度
     * @return 计算得到的采样率
     */
    private static int calculateInSampleSize(BitmapFactory.Options options, int reqWidth, int reqHeight) {
        // 原始图片的高度和宽度
        final int height = options.outHeight;
        final int width = options.outWidth;
        int inSampleSize = 1;

        if (height > reqHeight || width > reqWidth) {
            // 计算高度和宽度的比例
            final int heightRatio = Math.round((float) height / (float) reqHeight);
            final int widthRatio = Math.round((float) width / (float) reqWidth);

            // 选择较小的比例作为采样率，保证结果图片不小于请求的尺寸
            inSampleSize = Math.min(heightRatio, widthRatio);
            
            // 采样率必须是2的幂
            int power = 1;
            while (power * 2 <= inSampleSize) {
                power *= 2;
            }
            inSampleSize = power;
        }

        return inSampleSize;
    }
    
    /**
     * 获取文件扩展名
     * <p>
     * 从文件路径中提取文件扩展名，用于判断文件类型和选择相应的处理策略。
     * 扩展名会自动转换为小写以便进行不区分大小写的比较。
     * </p>
     *
     * <p>处理逻辑：</p>
     * <ul>
     *   <li>查找路径中最后一个点号的位置</li>
     *   <li>提取点号后的字符串作为扩展名</li>
     *   <li>自动转换为小写便于比较</li>
     *   <li>处理边界情况：空路径、无扩展名等</li>
     * </ul>
     *
     * @param filePath 文件的完整路径，可以为null
     * @return 小写的文件扩展名，如果没有扩展名或路径无效则返回空字符串
     */
    private static String getFileExtension(String filePath)
    {
        if (filePath == null || filePath.isEmpty())
        {
            return "";
        }

        int lastDot = filePath.lastIndexOf('.');
        if (lastDot >= 0 && lastDot < filePath.length() - 1)
        {
            return filePath.substring(lastDot + 1).toLowerCase();
        }

        return "";
    }


}