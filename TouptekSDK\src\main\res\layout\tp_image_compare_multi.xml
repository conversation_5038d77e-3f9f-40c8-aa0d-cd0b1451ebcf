<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:background="#000000">

    <!-- 工具栏 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:padding="8dp"
        android:background="#000000"
        android:elevation="4dp">
        
        <ImageButton
            android:id="@+id/btn_back"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:src="@android:drawable/ic_menu_revert"
            android:background="?attr/selectableItemBackgroundBorderless"
            android:contentDescription="返回" />
            
        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:layout_gravity="center_vertical"
            android:text="四图对比"
            android:textSize="18sp"
            android:textColor="#FFFFFF"
            android:gravity="center"
            android:textStyle="bold" />
            
        <ImageButton
            android:id="@+id/btn_sync_mode"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:src="@drawable/ic_sync"
            android:background="?attr/selectableItemBackgroundBorderless"
            android:contentDescription="同步模式" />

        <ImageButton
            android:id="@+id/btn_reset"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:src="@drawable/ic_refresh"
            android:background="?attr/selectableItemBackgroundBorderless"
            android:contentDescription="重置" />

        <ImageButton
            android:id="@+id/btn_swap"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:src="@drawable/ic_swap_horiz"
            android:background="?attr/selectableItemBackgroundBorderless"
            android:contentDescription="交换位置" />
            
    </LinearLayout>
    
    <!-- 四图对比区域 - 2x2网格布局 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:orientation="vertical">
        
        <!-- 上半部分：左上和右上 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="1"
            android:orientation="horizontal">
            
            <!-- 左上图片 -->
            <FrameLayout
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="1">
                
                <com.touptek.ui.TpImageView
                    android:id="@+id/image_top_left"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:background="#000000" />
                    
                <TextView
                    android:id="@+id/tv_info_top_left"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="bottom|start"
                    android:background="#CC000000"
                    android:textColor="#FFFFFF"
                    android:textSize="9sp"
                    android:padding="4dp"
                    android:text="图片1"
                    android:maxLines="2"
                    android:ellipsize="end" />
                    
            </FrameLayout>
            
            <!-- 分隔线 -->
            <View
                android:layout_width="2dp"
                android:layout_height="match_parent"
                android:background="#333333" />
            
            <!-- 右上图片 -->
            <FrameLayout
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="1">
                
                <com.touptek.ui.TpImageView
                    android:id="@+id/image_top_right"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:background="#000000" />
                    
                <TextView
                    android:id="@+id/tv_info_top_right"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="bottom|start"
                    android:background="#CC000000"
                    android:textColor="#FFFFFF"
                    android:textSize="9sp"
                    android:padding="4dp"
                    android:text="图片2"
                    android:maxLines="2"
                    android:ellipsize="end" />
                    
            </FrameLayout>
            
        </LinearLayout>
        
        <!-- 分隔线 -->
        <View
            android:layout_width="match_parent"
            android:layout_height="2dp"
            android:background="#333333" />
        
        <!-- 下半部分：左下和右下 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="1"
            android:orientation="horizontal">
            
            <!-- 左下图片 -->
            <FrameLayout
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="1">
                
                <com.touptek.ui.TpImageView
                    android:id="@+id/image_bottom_left"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:background="#000000" />
                    
                <TextView
                    android:id="@+id/tv_info_bottom_left"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="bottom|start"
                    android:background="#CC000000"
                    android:textColor="#FFFFFF"
                    android:textSize="9sp"
                    android:padding="4dp"
                    android:text="图片3"
                    android:maxLines="2"
                    android:ellipsize="end" />
                    
            </FrameLayout>
            
            <!-- 分隔线 -->
            <View
                android:layout_width="2dp"
                android:layout_height="match_parent"
                android:background="#333333" />
            
            <!-- 右下图片 -->
            <FrameLayout
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="1">
                
                <com.touptek.ui.TpImageView
                    android:id="@+id/image_bottom_right"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:background="#000000" />
                    
                <TextView
                    android:id="@+id/tv_info_bottom_right"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="bottom|start"
                    android:background="#CC000000"
                    android:textColor="#FFFFFF"
                    android:textSize="9sp"
                    android:padding="4dp"
                    android:text="图片4"
                    android:maxLines="2"
                    android:ellipsize="end" />
                    
            </FrameLayout>
            
        </LinearLayout>
        
    </LinearLayout>
    
</LinearLayout>
