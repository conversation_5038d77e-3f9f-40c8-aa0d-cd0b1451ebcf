package com.touptek.video.internal;

import android.content.Context;
import android.media.tv.TvContract;
import android.media.tv.TvView;
import android.net.Uri;
import android.os.Handler;
import android.os.Looper;
import android.util.Log;
import android.view.ViewGroup;

/**
 * TpTvPreview 类用于提供基于TvView的低延迟预览功能。
 * <p>
 * 此类封装了TvView的创建和管理，提供简单的接口控制TV模式预览。
 * </p>
 */
public class TpTvPreview {
    private static final String TAG = "TpTvPreview";
    private static final String INPUT_ID = "com.example.partnersupportsampletvinput/.SampleTvInputService/HW0";
    private static final int MSG_START_TV = 0;
    private static final long START_TV_REVIEW_DELAY = 10L;

    /* 上下文 */
    private final Context context;
    
    /* 容器视图 */
    private final ViewGroup containerView;
    
    /* TV预览视图 */
    private TvView tvView;
    
    /* 频道URI */
    private final Uri channelUri;

    /* 处理延迟启动TV预览的Handler */
    private final Handler handler = new Handler(Looper.getMainLooper()) {
        public void handleMessage(android.os.Message msg) {
            if (msg.what == MSG_START_TV) {
                if (tvView != null && channelUri != null) {
                    Log.d(TAG, "Tuning TV to channel: " + channelUri);
                    tvView.tune(INPUT_ID, channelUri);
                }
            }
        }
    };

    /**
     * 构造函数，初始化TV预览助手。
     *
     * @param context 应用上下文
     * @param containerView 用于放置TvView的容器视图
     */
    public TpTvPreview(Context context, ViewGroup containerView) {
        this.context = context;
        this.containerView = containerView;
        this.channelUri = TvContract.buildChannelUriForPassthroughInput(INPUT_ID);
    }

    /**
     * 初始化TvView。
     */
    private void initTvView() {
        // 如果已经存在TvView，先移除
        if (tvView != null) {
            containerView.removeView(tvView);
            tvView = null;
        }

        // 创建新的TvView
        tvView = new TvView(context);
        tvView.setLayoutParams(new ViewGroup.LayoutParams(
                ViewGroup.LayoutParams.MATCH_PARENT,
                ViewGroup.LayoutParams.MATCH_PARENT));
        
        // 添加到容器中
        containerView.addView(tvView);
    }

    /**
     * 启动TV预览。
     */
    public void startPreview() {
        initTvView();
        handler.removeMessages(MSG_START_TV);
        handler.sendEmptyMessageDelayed(MSG_START_TV, START_TV_REVIEW_DELAY);
        Log.d(TAG, "Starting TV preview");
    }

    /**
     * 停止TV预览。
     */
    public void stopPreview() {
        handler.removeMessages(MSG_START_TV);
        if (tvView != null) {
            tvView.reset();
            containerView.removeView(tvView);
            tvView = null;
        }
        Log.d(TAG, "Stopping TV preview");
    }

    /**
     * 释放资源。
     */
    public void release() {
        stopPreview();
        handler.removeCallbacksAndMessages(null);
    }
    
    /**
     * 检查TV预览是否处于活动状态。
     *
     * @return 如果TV预览处于活动状态则返回true，否则返回false
     */
    public boolean isActive() {
        return tvView != null;
    }
}
